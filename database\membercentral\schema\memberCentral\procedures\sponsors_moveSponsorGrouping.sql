CREATE PROC dbo.sponsors_moveSponsorGrouping
@siteID int,
@sponsorGroupingID int,
@dir varchar(4),
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @dir NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	DECLARE @currentOrder int;

	SELECT @currentOrder = sponsorGroupingOrder
	FROM dbo.sponsorsGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	IF @currentOrder IS NULL
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder + 1
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingOrder >= @currentOrder - 1;

			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder - 2
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingID = @sponsorGroupingID;
		END
		ELSE BEGIN
			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder - 1
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingOrder <= @currentOrder + 1;

			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder + 2
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingID = @sponsorGroupingID;
		END

		EXEC dbo.sponsors_reorderSponsorGroupings @siteID=@siteID, @referenceType=@referenceType, @referenceID=@referenceID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
