USE memberCentral;
GO

CREATE TABLE dbo.sponsorsGrouping(
	sponsorGrouping<PERSON> int IDENTITY(1,1) NOT NULL,
	sponsorGrouping varchar(200) NOT NULL,
	sponsorGroupingOrder int NOT NULL,
	siteID int NOT NULL,
	referenceType varchar(20) NOT NULL,
	referenceID varchar(20) NOT NULL,
 CONSTRAINT PK_sponsorsGrouping PRIMARY KEY CLUSTERED
(
	sponsorGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE dbo.sponsorsGrouping WITH CHECK ADD  CONSTRAINT FK_sponsorsGrouping_sites FOREIGN KEY(siteID)
REFERENCES dbo.sites (siteID)
GO

CREATE TABLE dbo.sponsorsUsageGrouping(
	sponsorUsageGroupingID INT IDENTITY(1,1) NOT NULL,
	sponsorUsageID INT NOT NULL,
	sponsorGroupingID INT NOT NULL,
 CONSTRAINT PK_sponsorsUsageGrouping PRIMARY KEY CLUSTERED
(
	sponsorUsageGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE dbo.sponsorsUsageGrouping WITH CHECK ADD  CONSTRAINT FK_sponsorsUsageGrouping_sponsorsUsage FOREIGN KEY(sponsorUsageID)
REFERENCES dbo.sponsorsUsage (sponsorUsageID)
GO

ALTER TABLE dbo.sponsorsUsageGrouping WITH CHECK ADD  CONSTRAINT FK_sponsorsUsageGrouping_sponsorsGrouping FOREIGN KEY(sponsorGroupingID)
REFERENCES dbo.sponsorsGrouping (sponsorGroupingID)
GO

CREATE PROC dbo.sponsors_createSponsorGrouping
@siteID int,
@referenceType varchar(20),
@referenceID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sponsorGroupingOrder int;

	SET @sponsorGroupingID = NULL;

	IF EXISTS (
		SELECT sponsorGroupingID
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceID = @referenceID
		AND referenceType = @referenceType
		AND sponsorGrouping = @sponsorGrouping
	)
		RAISERROR('Sponsor Grouping Exists.',16,1);

	SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1
	FROM dbo.sponsorsGrouping
	WHERE siteID = @siteID
	AND referenceID = @referenceID
	AND referenceType = @referenceType;
			
	INSERT INTO dbo.sponsorsGrouping(sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, referenceID)
	VALUES (@sponsorGrouping, @sponsorGroupingOrder, @siteID, @referenceType, @referenceID);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_updateSponsorGrouping
@siteID int,
@sponsorGroupingID int,
@sponsorGrouping varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF EXISTS (SELECT sponsorGroupingID FROM dbo.sponsorsGrouping WHERE siteID = @siteID AND sponsorGroupingID != @sponsorGroupingID AND sponsorGrouping = @sponsorGrouping)
		RAISERROR('Sponsor grouping name already exists.',16,1);

	UPDATE dbo.sponsorsGrouping
	SET sponsorGrouping = @sponsorGrouping
	WHERE siteID = @siteID
	AND sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_reorderSponsorGroupings
@siteID int,
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmpGroupings TABLE (sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL, neworder int NOT NULL);

	INSERT INTO @tmpGroupings (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
	FROM dbo.sponsorsGrouping
	WHERE siteID = @siteID
	AND referenceType = @referenceType
	AND referenceID = @referenceID;

	UPDATE sg
	SET sg.sponsorGroupingOrder = tmp.neworder
	FROM dbo.sponsorsGrouping as sg
	INNER JOIN @tmpGroupings as tmp on tmp.sponsorGroupingID = sg.sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_deleteSponsorGrouping
@siteID int,
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Check if any sponsors are using this grouping
	IF EXISTS (SELECT sponsorUsageGroupingID FROM dbo.sponsorsUsageGrouping WHERE sponsorGroupingID = @sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	IF EXISTS (SELECT 1 FROM dbo.sponsorsGrouping WHERE sponsorGroupingID = @sponsorGroupingID AND siteID <> @siteID)
		RAISERROR('Invalid action.',16,1);

	DECLARE @referenceType varchar(20), @referenceID int;

	SELECT @referenceType = referenceType, @referenceID = referenceID
	FROM dbo.sponsorsGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	DELETE FROM dbo.sponsorsGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- reorder remaining groupings
	EXEC dbo.sponsors_reorderSponsorGroupings @siteID=@siteID, @referenceType=@referenceType, @referenceID=@referenceID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_moveSponsorGrouping
@siteID int,
@sponsorGroupingID int,
@dir varchar(4),
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @dir NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	DECLARE @currentOrder int;

	SELECT @currentOrder = sponsorGroupingOrder
	FROM dbo.sponsorsGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	IF @currentOrder IS NULL
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder + 1
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingOrder >= @currentOrder - 1;

			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder - 2
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingID = @sponsorGroupingID;
		END
		ELSE BEGIN
			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder - 1
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingOrder <= @currentOrder + 1;

			UPDATE dbo.sponsorsGrouping
			SET sponsorGroupingOrder = sponsorGroupingOrder + 2
			WHERE siteID = @siteID
			AND referenceType = @referenceType
			AND referenceID = @referenceID
			AND sponsorGroupingID = @sponsorGroupingID;
		END

		EXEC dbo.sponsors_reorderSponsorGroupings @siteID=@siteID, @referenceType=@referenceType, @referenceID=@referenceID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_getSponsorGroupings
@siteID int,
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	WITH SponsorGroupings AS (
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = @referenceType
		AND referenceID = @referenceID
			UNION ALL
		SELECT 0, 'Default - No Grouping', 0
	)
	SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
	FROM SponsorGroupings
	ORDER BY sponsorGroupingOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.sponsors_getSponsorGroupingByID
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT sponsorGroupingID, sponsorGrouping, referenceID, referenceType
	FROM dbo.sponsorsGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_getSponsorsByReferenceIDFull
@siteID int,
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @featureImageConfigReferenceType varchar(30), @featureImageReferenceType varchar(30), @featureImageSizeReferenceType varchar(30);

	SET @featureImageConfigReferenceType = 'evSiteSponsor';
	SET @featureImageReferenceType = 'EventSponsors';

	IF @referenceType = 'Events'
		SET @featureImageSizeReferenceType = 'viewEventDetails';
	IF @referenceType IN ('swlProgram', 'swodProgram', 'swbProgram')
		SET @featureImageSizeReferenceType = 'viewSemwebDetails';

	SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId, 
		sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID, 
		fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
		ISNULL(sg.sponsorGroupingID, 0) AS sponsorGroupingID, sg.sponsorGrouping, sg.sponsorGroupingOrder,ISNULL(sg.sponsorGroupingOrder, 999) AS sponsorGroupingOrderDefault
	FROM dbo.sponsorsUsage as su
	INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping as sug on sug.sponsorUsageID = su.sponsorUsageID
	LEFT OUTER JOIN dbo.sponsorsGrouping as sg on sg.sponsorGroupingID = sug.sponsorGroupingID
	LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
		AND fiu.referenceType = @featureImageReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
	LEFT OUTER JOIN dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
		AND ficus.referenceType = @featureImageSizeReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes as fics on fics.featureImageSizeID = ficus.featureImageSizeID
	CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
	WHERE su.referenceType = @referenceType
	AND su.referenceID = @referenceID
	ORDER BY ISNULL(sg.sponsorGroupingOrder, 999), su.sponsorOrder;
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_reorderSponsorsInUsage
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (sponsorUsageID INT NOT NULL, sponsorOrder INT NOT NULL, neworder INT NOT NULL);
	
	INSERT INTO @tmp (sponsorUsageID, sponsorOrder, newOrder)
	SELECT u.sponsorUsageID, sponsorOrder, ROW_NUMBER() OVER (PARTITION BY ug.sponsorGroupingID ORDER BY u.sponsorOrder, u.sponsorUsageID) AS newOrder
	FROM dbo.sponsorsUsage AS u
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS ug ON ug.sponsorUsageID = u.sponsorUsageID
	WHERE referenceType = @referenceType
	AND referenceID = @referenceID;
	
	UPDATE su
	SET su.sponsorOrder = t.neworder
	FROM dbo.sponsorsUsage su
	INNER JOIN @tmp t ON su.sponsorUsageID = t.sponsorUsageID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_moveSponsor
@siteID INT,
@sponsorUsageID INT,
@referenceType VARCHAR(50),
@referenceID INT,
@dir VARCHAR(4),
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;

BEGIN TRY
	
	DECLARE @orgID int, @sponsorID int, @sponsorGroupingID int, @sponsorOrder int;

	SELECT @sponsorID = sponsorID, @sponsorOrder = sponsorOrder, @sponsorGroupingID = ug.sponsorGroupingID
	FROM dbo.sponsorsUsage AS u
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS ug ON ug.sponsorUsageID = u.sponsorUsageID
	WHERE u.sponsorUsageID = @sponsorUsageID;

	IF NOT EXISTS (SELECT sponsorID FROM dbo.sponsors WHERE siteID = @siteID AND sponsorID = @sponsorID)
		RAISERROR('Invalid Sponsor',16,1);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE u
			SET u.sponsorOrder = u.sponsorOrder + 1
			FROM dbo.sponsorsUsage AS u
			LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS ug ON ug.sponsorUsageID = u.sponsorUsageID
			WHERE u.referenceType = @referenceType
			AND u.referenceID = @referenceID
			AND ISNULL(ug.sponsorGroupingID,0) = ISNULL(@sponsorGroupingID,0)
			AND sponsorOrder >= @sponsorOrder - 1;

			UPDATE dbo.sponsorsUsage
			SET sponsorOrder = sponsorOrder - 2
			WHERE sponsorUsageID = @sponsorUsageID;
		END
		ELSE BEGIN
			UPDATE u
			SET u.sponsorOrder = u.sponsorOrder -1
			FROM dbo.sponsorsUsage AS u
			LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS ug ON ug.sponsorUsageID = u.sponsorUsageID
			WHERE u.referenceType = @referenceType
			AND u.referenceID = @referenceID
			AND ISNULL(ug.sponsorGroupingID,0) = ISNULL(@sponsorGroupingID,0)
			AND sponsorOrder <= @sponsorOrder + 1;

			UPDATE dbo.sponsorsUsage
			SET sponsorOrder = sponsorOrder + 2
			WHERE sponsorUsageID = @sponsorUsageID;
		END

	COMMIT TRAN;

	EXEC dbo.sponsors_reorderSponsorsInUsage @referenceType=@referenceType, @referenceID=@referenceID;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"auditLog", "d": {
		"AUDITCODE":"SPNSR",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('[' + sponsorName + '] moved '+@dir+' to associated ' + @referenceType + '-' + cast(@referenceID AS VARCHAR(20)) + '.'),'"','\"') + '" } }'
	FROM dbo.sponsors
	WHERE sponsorID = @sponsorID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_associateSponsor
@siteID int,
@sponsorID int,
@referenceType varchar(20),
@referenceID int,
@recordedByMemberID int,
@sponsorGroupingID int,
@sponsorsUsageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @orgID int;

	-- verify sponsorID is part of site
	IF NOT EXISTS (SELECT sponsorID FROM dbo.sponsors WHERE siteID = @siteID AND sponsorID = @sponsorID)
		RAISERROR('Invalid Sponsor',16,1);

	SET @sponsorsUsageID = NULL;
	
	SELECT @sponsorsUsageID = sponsorUsageID
	FROM dbo.sponsorsUsage
	WHERE sponsorID = @sponsorID
	AND referenceType = @referenceType
	AND referenceID = @referenceID

	IF @sponsorsUsageID IS NULL BEGIN
		INSERT INTO dbo.sponsorsUsage (sponsorID, referenceType, referenceID, sponsorOrder)
		VALUES (@sponsorID, @referenceType, @referenceID, 999);

		SELECT @sponsorsUsageID = SCOPE_IDENTITY();

		IF @sponsorGroupingID IS NOT NULL
			INSERT INTO dbo.sponsorsUsageGrouping(sponsorUsageID, sponsorGroupingID)
			VALUES (@sponsorsUsageID, @sponsorGroupingID);

		EXEC dbo.sponsors_reorderSponsorsInUsage @referenceType=@referenceType, @referenceID=@referenceID;

		SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"auditLog", "d": {
			"AUDITCODE":"SPNSR",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('[' + sponsorName + '] was associated to ' + @referenceType + '-' + cast(@referenceID AS VARCHAR(20)) + '.'),'"','\"') + '" } }'
		FROM dbo.sponsors
		WHERE sponsorID = @sponsorID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_deassociateSponsor
@siteID INT,
@sponsorUsageID INT,
@referenceType VARCHAR(20),
@referenceID INT,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @orgID INT;

	-- verify sponsorID is part of site
	DECLARE @sponsorID int;
	SELECT @sponsorID = sponsorID FROM dbo.sponsorsUsage WHERE sponsorUsageID = @sponsorUsageID;

	IF NOT EXISTS (SELECT sponsorID FROM dbo.sponsors WHERE siteID = @siteID AND sponsorID = @sponsorID)
		RAISERROR('Invalid Sponsor',16,1);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	DELETE FROM dbo.sponsorsUsageGrouping
	WHERE sponsorUsageID = @sponsorUsageID;

	DELETE FROM dbo.sponsorsUsage
	WHERE sponsorUsageID = @sponsorUsageID;
	
	EXEC dbo.sponsors_reorderSponsorsInUsage @referenceType=@referenceType, @referenceID=@referenceID;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"auditLog", "d": {
		"AUDITCODE":"SPNSR",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('[' + sponsorName + '] was deassociated with ' + @referenceType + '-' + cast(@referenceID AS VARCHAR(20)) + '.'),'"','\"') + '" } }'
	FROM dbo.sponsors
	WHERE sponsorID = @sponsorID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sponsors_getSponsorsByReferenceID
@siteID int,
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	WITH SponsorsGroupings AS (
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = @referenceType
		AND referenceID = @referenceID
			UNION ALL
		SELECT 0, 'Default - No Grouping', 0
	)
	SELECT sg.sponsorGroupingID, sg.sponsorGrouping, sg.sponsorGroupingOrder, su.sponsorUsageID, s.sponsorID, s.sponsorName, su.sponsorOrder
	FROM dbo.sponsorsUsage AS su
	INNER JOIN dbo.sponsors as s on s.siteID = @siteID
		AND s.sponsorID = su.sponsorID
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS sug ON sug.sponsorUsageID = su.sponsorUsageID
	LEFT OUTER JOIN SponsorsGroupings AS sg ON sg.sponsorGroupingID = ISNULL(sug.sponsorGroupingID,0)
	WHERE su.referenceType = @referenceType
	AND su.referenceID = @referenceID
	ORDER BY sg.sponsorGroupingOrder, su.sponsorOrder;
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES ('updateSponsorGrouping', @adminViewRTFID),
		('moveSponsorGrouping', @adminViewRTFID),
		('deleteSponsorGrouping', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='SPONSORS',
		@requestCFC='model.admin.common.modules.sponsors.sponsors',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

Use seminarweb
GO

CREATE PROC dbo.swod_copySponsorWithGroupings
@siteID int,
@copyFromSeminarID int,
@copyToSeminarID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @sponsorsUsageID int;

	-- Create temp table to store the mapping of old to new sponsor grouping IDs
	IF OBJECT_ID('tempdb..#tmpSponsorGroupingMapping') IS NOT NULL
		DROP TABLE #tmpSponsorGroupingMapping;
	CREATE TABLE #tmpSponsorGroupingMapping (
		oldSponsorGroupingID int,
		newSponsorGroupingID int,
		sponsorGrouping varchar(200),
		sponsorGroupingOrder int
	);

	BEGIN TRAN;
		
		-- Copy sponsor groupings from source to destination seminar
		INSERT INTO membercentral.dbo.sponsorsGrouping (sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, referenceID)
		OUTPUT inserted.sponsorGroupingID, 0, inserted.sponsorGrouping, inserted.sponsorGroupingOrder
			INTO #tmpSponsorGroupingMapping (newSponsorGroupingID, oldSponsorGroupingID, sponsorGrouping, sponsorGroupingOrder)
		SELECT sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, @copyToSeminarID
		FROM membercentral.dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = 'swodProgram'
		AND referenceID = @copyFromSeminarID;

		-- Update the mapping with the original sponsor grouping IDs
		UPDATE tmp
		SET tmp.oldSponsorGroupingID = orig.sponsorGroupingID
		FROM #tmpSponsorGroupingMapping tmp
		INNER JOIN membercentral.dbo.sponsorsGrouping orig ON orig.siteID = @siteID
			AND orig.referenceID = @copyFromSeminarID
			AND orig.referenceType = 'swodProgram'
			AND orig.sponsorGrouping = tmp.sponsorGrouping
			AND orig.sponsorGroupingOrder = tmp.sponsorGroupingOrder;

		-- Copy sponsors with their grouping associations
		DECLARE @sponsorID int, @oldSponsorGroupingID int, @newSponsorGroupingID int;
		DECLARE sponsor_cursor CURSOR FOR
			SELECT DISTINCT su.sponsorID, sug.sponsorGroupingID
			FROM membercentral.dbo.sponsorsUsage su
			LEFT OUTER JOIN membercentral.dbo.sponsorsUsageGrouping sug ON sug.sponsorUsageID = su.sponsorUsageID
			INNER JOIN membercentral.dbo.sponsors s ON s.sponsorID = su.sponsorID AND s.siteID = @siteID
			WHERE su.referenceType = 'swodProgram'
			AND su.referenceID = @copyFromSeminarID;

		OPEN sponsor_cursor;
		FETCH NEXT FROM sponsor_cursor INTO @sponsorID, @oldSponsorGroupingID;

		WHILE @@FETCH_STATUS = 0
		BEGIN
			-- Map old sponsor grouping ID to new one (NULL if no grouping)
			SET @newSponsorGroupingID = NULL;
			IF @oldSponsorGroupingID IS NOT NULL
			BEGIN
				SELECT @newSponsorGroupingID = newSponsorGroupingID
				FROM #tmpSponsorGroupingMapping
				WHERE oldSponsorGroupingID = @oldSponsorGroupingID;
			END

			EXEC membercentral.dbo.sponsors_associateSponsor
				@siteID = @siteID,
				@sponsorID = @sponsorID,
				@referenceType = 'swodProgram',
				@referenceID = @copyToSeminarID,
				@recordedByMemberID = @recordedByMemberID,
				@sponsorGroupingID = @newSponsorGroupingID,
				@sponsorsUsageID=@sponsorsUsageID OUTPUT;

			FETCH NEXT FROM sponsor_cursor INTO @sponsorID, @oldSponsorGroupingID;
		END;

		CLOSE sponsor_cursor;
		DEALLOCATE sponsor_cursor;

	COMMIT TRAN;

	-- Clean up temp table
	IF OBJECT_ID('tempdb..#tmpSponsorGroupingMapping') IS NOT NULL
		DROP TABLE #tmpSponsorGroupingMapping;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO