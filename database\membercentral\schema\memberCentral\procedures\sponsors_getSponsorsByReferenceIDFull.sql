ALTER PROC dbo.sponsors_getSponsorsByReferenceIDFull
@siteID int,
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @featureImageConfigReferenceType varchar(30), @featureImageReferenceType varchar(30), @featureImageSizeReferenceType varchar(30);

	SET @featureImageConfigReferenceType = 'evSiteSponsor';
	SET @featureImageReferenceType = 'EventSponsors';

	IF @referenceType = 'Events'
		SET @featureImageSizeReferenceType = 'viewEventDetails';
	IF @referenceType IN ('swlProgram', 'swodProgram', 'swbProgram')
		SET @featureImageSizeReferenceType = 'viewSemwebDetails';

	SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId, 
		sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID, 
		fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
		ISNULL(sg.sponsorGroupingID, 0) AS sponsorGroupingID, sg.sponsorGrouping, sg.sponsorGroupingOrder,ISNULL(sg.sponsorGroupingOrder, 999) AS sponsorGroupingOrderDefault
	FROM dbo.sponsorsUsage as su
	INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping as sug on sug.sponsorUsageID = su.sponsorUsageID
	LEFT OUTER JOIN dbo.sponsorsGrouping as sg on sg.sponsorGroupingID = sug.sponsorGroupingID
	LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
		AND fiu.referenceType = @featureImageReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
	LEFT OUTER JOIN dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
		AND ficus.referenceType = @featureImageSizeReferenceType
	LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes as fics on fics.featureImageSizeID = ficus.featureImageSizeID
	CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
	WHERE su.referenceType = @referenceType
	AND su.referenceID = @referenceID
	ORDER BY ISNULL(sg.sponsorGroupingOrder, 999), su.sponsorOrder;
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO