CREATE PROC dbo.sponsors_updateSponsorGrouping
@siteID int,
@sponsorGroupingID int,
@sponsorGrouping varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF EXISTS (SELECT sponsorGroupingID FROM dbo.sponsorsGrouping WHERE siteID = @siteID AND sponsorGroupingID != @sponsorGroupingID AND sponsorGrouping = @sponsorGrouping)
		RAISERROR('Sponsor grouping name already exists.',16,1);

	UPDATE dbo.sponsorsGrouping
	SET sponsorGrouping = @sponsorGrouping
	WHERE siteID = @siteID
	AND sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO