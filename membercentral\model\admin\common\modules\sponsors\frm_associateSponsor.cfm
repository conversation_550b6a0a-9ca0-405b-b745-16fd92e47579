<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function associateSponsorWithGroup() {
			mca_hideAlert('err_sponsor');

			var sponsorGroupingID = $('##sponsorGroupingID').val();
			if ($('##sponsorGroupingID').val() == 'new' && $('##newSponsorGroupingName').val().trim() == ''){
				mca_showAlert('err_sponsor', 'Enter the new sponsor grouping name.');
				return false;
			}

			var associateSponsorResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.loadSponsors_#arguments.widgetSelectorID#();
					top.MCModalUtils.hideModal();
				} else {
					alert('Unable to associate this sponsor with the selected sponsor grouping.');
					top.$('##btnMCModalSave').prop('disabled',false);
				}
			};

			top.$('##btnMCModalSave').prop('disabled',true);

			var objParams = {
				sponsorID: #val(local.qrySponsor.sponsorID)#,
				referenceType: '#arguments.referenceType#',
				referenceID: #arguments.referenceID#,
				sponsorGroupingID: sponsorGroupingID == 'new' ? 0 : sponsorGroupingID,
				newSponsorGroupingName: $('##newSponsorGroupingName').val()
			};

			TS_AJX('SPONSORS','associateSponsor',objParams,associateSponsorResult,associateSponsorResult,10000,associateSponsorResult);
		}
		function onChangeSponsorGrouping(sgID) {
			if (sgID == 'new') {
				$('##newSponsorGroupingRow').removeClass('d-none');
			} else {
				$('##newSponsorGroupingRow').addClass('d-none');
				$('##newSponsorGroupingName').val('');
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="err_sponsor" class="alert alert-danger mb-2 d-none"></div>
<form name="frmSponsor" id="frmSponsor" class="p-3">
	<div class="mb-3">
		<h6>Sponsor: <strong>#local.qrySponsor.sponsorName#</strong></h6>
	</div>
	<div class="form-group">
		<div class="form-label-group mb-3">
			<select name="sponsorGroupingID" id="sponsorGroupingID" class="form-control" onchange="onChangeSponsorGrouping(this.value);">
				<cfloop query="local.qryGroupings">
					<option value="#local.qryGroupings.sponsorGroupingID#">#encodeForHTML(local.qryGroupings.sponsorGrouping)#</option>
				</cfloop>
				<option value="new">Add New Grouping</option>
			</select>
			<label for="sponsorGroupingID">Sponsor Grouping</label>
		</div>
	</div>
	<div class="form-group d-none" id="newSponsorGroupingRow">
		<div class="form-label-group">
			<input type="text" name="newSponsorGroupingName" id="newSponsorGroupingName" value="" class="form-control" maxlength="200" autocomplete="off">
			<label for="newSponsorGroupingName">Name of Sponsor Grouping</label>
		</div>
	</div>
</form>
</cfoutput>