<cfcomponent extends="model.admin.admin" output="no">
	<cfset this.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(
				siteResourceID=this.siteResourceID, 
				memberID=session.cfcuser.memberdata.memberID, 
				siteID=arguments.event.getValue('mc_siteInfo.siteid')
				);
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			this.link.list = buildCurrentLink(arguments.event,"list");
			this.link.edit = buildCurrentLink(arguments.event,"edit") & "&mode=direct";

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="prepResult" type="string" required="false">
	
		<cfset var local = structNew()>
		<cfset local.advanceFormulasLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=advanceFormulasJSON&meth=getAdvancementFormulas&mode=stream">
		<cfset local.exportFormulaStructureZIPLink = buildCurrentLink(arguments.event,"exportAdvanceFormulaStructureZIP") & "&mode=stream">
		<cfset local.prepareFormulaImportLink = buildCurrentLink(arguments.event,"prepareAdvanceFormulasImport")>
		<cfset local.doImportFormulaLink = buildCurrentLink(arguments.event,"doImportAdvanceFormulas") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_formulaList.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeAF" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="afid" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDelAF">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @afid int, @inUse bit;
					set @afid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afid#">;
					set @inUse = dbo.fn_af_isInUse(@afid);

					IF (@inUse = 0)
						DELETE from dbo.af_advanceFormulas
						where afid = @afid
						and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						and isSystemOnly = 0;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.afID = int(val(arguments.event.getValue('afid',0)))>
		<cfset local.AFCurrentDate = dateformat(now(),"m/d/yyyy")>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAF">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select af.AFID, af.afName, af.datePart, af.dateNum, af.adjustTerm, af.nextWeekday, af.weekNumber, af.uid, af.isSystemOnly
			from dbo.af_advanceFormulas af
			where af.AFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AFID#">
			and af.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.hasSaveRights = true>
		<cfif local.qryAF.recordCount>
			<cfquery name="local.qryAFUsage" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.af_getAdvanceFormulaUsage @afID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AFID#">
			</cfquery>
			<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qryAF.isSystemOnly EQ 1>
				<cfset local.hasSaveRights = false>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_advanceFormula.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAdvanceFormulaDateforAFID" access="public" output="false" returntype="struct">
		<cfargument name="baseDate" type="string" required="true">
		<cfargument name="afid" type="numeric" required="true">

		<cfset var qryAF = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAF">
			SELECT [datePart], dateNum, adjustTerm, nextWeekday, weekNumber
			FROM dbo.af_advanceFormulas
			WHERE AFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afid#">
		</cfquery>
		
		<cfreturn getAdvanceFormulaDate(baseDate=arguments.baseDate, datePart=qryAF.datePart, dateNum=qryAF.dateNum, adjustTerm=qryAF.adjustTerm, nextWeekday=qryAF.nextWeekday, weekNumber=qryAF.weekNumber)>
	</cffunction>

	<cffunction name="getAdvanceFormulaDate" access="public" output="false" returntype="struct">
		<cfargument name="baseDate" type="string" required="true">
		<cfargument name="datePart" type="string" required="true">
		<cfargument name="dateNum" type="numeric" required="true">
		<cfargument name="adjustTerm" type="string" required="true">
		<cfargument name="nextWeekday" type="numeric" required="true">
		<cfargument name="weekNumber" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAFDate">
			set nocount on;

			DECLARE @baseDate DATETIME, @retDate DATETIME;
			SET @baseDate = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.baseDate#">;

			select @retDate = dbo.fn_af_getAFDate(@baseDate,<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.datePart#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.dateNum#">,<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.adjustTerm#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.nextWeekDay#">,<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.weekNumber#">);

			SELECT convert(varchar,@baseDate,101) AS baseDate, convert(varchar,@retDate,101) AS retDate;
		</cfquery>
		
		<cfif len(local.qryAFDate.retDate)>
			<cfset local.data.success = true>
			<cfset local.data.basedate = dateformat(local.qryAFDate.baseDate,"m/d/yyyy")>
			<cfset local.data.retdate = dateformat(local.qryAFDate.retDate,"m/d/yyyy")>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveAdvanceFormula" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="afid" type="numeric" required="true">
		<cfargument name="afName" type="string" required="true">
		<cfargument name="dateNum" type="numeric" required="true">
		<cfargument name="datePart" type="string" required="true">
		<cfargument name="adjustTerm" type="string" required="true">
		<cfargument name="nextWeekday" type="boolean" required="true">
		<cfargument name="weekNumber" type="string" required="true">
		<cfargument name="afUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":"" }>

		<cfif arguments.afid>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAF">
				select af.AFID, af.afName, af.datePart, af.dateNum, af.adjustTerm, af.nextWeekday, af.weekNumber, af.uid, af.isSystemOnly
				from dbo.af_advanceFormulas af
				where af.AFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afid#">
				and af.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			</cfquery>
			<cfif local.qryAF.isSystemOnly is 1>
				<cfset arguments.afName = local.qryAF.afName>
				<cfset arguments.dateNum = local.qryAF.dateNum>
				<cfset arguments.datePart = local.qryAF.datePart>
				<cfset arguments.adjustTerm = local.qryAF.adjustTerm>
				<cfset arguments.nextWeekday = local.qryAF.nextWeekday>
				<cfset arguments.weekNumber = local.qryAF.weekNumber>
			</cfif>
		</cfif>

		<!--- must have name --->
		<cfif NOT len(trim(arguments.afName))>
			<cfset local.returnStruct = { "success":false, "errmsg":"An advancement formula label cannot be blank." }>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- dateNum must be gt 0 --->
		<cfif arguments.dateNum lte 0>
			<cfset local.returnStruct = { "success":false, "errmsg":"The advancement number must be more than 0." }>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif arguments.datePart NEQ "M" AND arguments.adjustTerm NEQ "">
			<cfset arguments.adjustTerm = "">
		</cfif>
		<cfif arguments.datePart EQ "M" AND arguments.adjustTerm EQ "SameDayWk" AND arguments.nextWeekday is 1>
			<cfset arguments.nextWeekday = 0>
		</cfif>
		<cfif NOT (arguments.datePart EQ "M" AND arguments.adjustTerm EQ "SameDayWk")>
			<cfset arguments.weekNumber = "">
		</cfif>

		<!--- save formula --->
		<cfquery name="local.qrySaveAF" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @afID int, @datePart varchar(11), @dateNum smallint, @nextWeekday bit, @afName varchar(200),
					@uid uniqueidentifier, @adjustTerm varchar(12), @weekNumber varchar(4), @matchingAFID int, 
					@errMsg varchar(300) = '';

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @afID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afid#">;
				SET @afName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.afName)#">;
				SET @datePart = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.datePart)#">;
				SET @dateNum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.dateNum#">;
				SET @adjustTerm = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.adjustTerm#">,'');
				SET @nextWeekday = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.nextWeekday#">;
				SET @weekNumber = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.weekNumber#">,'');
				
				IF EXISTS (SELECT 1 FROM dbo.af_advanceFormulas WHERE afName = @afName AND siteID = @siteID <cfif arguments.afid>AND afID <> @afID</cfif>) BEGIN
					SET @errMsg = 'An advancement formula with that name already exists.';
					GOTO on_done;
				END

				SELECT @matchingAFID = afID
				FROM dbo.af_advanceFormulas 
				WHERE siteID = @siteID 
				<cfif arguments.afid>AND afID <> @afID</cfif>
				AND datePart = @datePart
				AND dateNum = @dateNum
				AND nextWeekday = @nextWeekday
				AND ISNULL(adjustTerm,'') = ISNULL(@adjustTerm,'')
				AND ISNULL(weekNumber,'') = ISNULL(@weekNumber,'');

				IF @matchingAFID IS NOT NULL BEGIN
					SELECT @errMsg = 'Advancement Formula ' + QUOTENAME(afName) + ' matches the existing set up.'
					FROM dbo.af_advanceFormulas 
					WHERE siteID = @siteID
					AND afID = @matchingAFID;

					GOTO on_done;
				END

				<cfif arguments.afid is 0>
					INSERT INTO dbo.af_advanceFormulas (siteID, afName, datePart, dateNum, adjustTerm, nextWeekday, weekNumber, isSystemOnly)
					VALUES (@siteID, @afName, @datePart, @dateNum, @adjustTerm, @nextWeekday, @weekNumber, 0);

					SET @afID = SCOPE_IDENTITY();
				<cfelse>
					UPDATE dbo.af_advanceFormulas
					SET afName = @afName,
						datePart = @datePart,
						dateNum = @dateNum,
						adjustTerm = @adjustTerm,
						nextWeekday = @nextWeekday,
						weekNumber = @weekNumber
						<cfif len(arguments.afUID) AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
							, [uid] = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.afUID#">
						</cfif>
					WHERE AFID = @afID
					AND siteID = @siteID
					<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						AND isSystemOnly = 0
					</cfif>;
				</cfif>

				on_done:
				SELECT @afID AS afID, @errMSg AS errMSg;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStruct = { "success": len(local.qrySaveAF.errMSg) ? false : true , "errmsg":local.qrySaveAF.errMSg }>		

		<cfreturn local.returnStruct>
	</cffunction>

	<!--- export/import --->
	<cffunction name="exportAdvanceFormulaStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "AdvFormulaStructure.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="af_exportAdvanceFormulaStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="false">
		</cfif>
	</cffunction>

	<cffunction name="prepareAdvanceFormulasImport" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">
	
		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/AdvFormulaStructure.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/AdvFormulaStructure.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/AdvFormulaStructure.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_af_advanceFormulas.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 1>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain only one.">
				<cfelseif local.qryFilesCheck.theCount neq 1>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/AdvFormulaStructure.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/AdvFormulaStructure.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>
		<!--- prepare import --->
		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Advance Formulas Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			<cfset local.AdvFormImportStruct = application.mcCacheManager.sessionGetValue(keyname='AdvFormImportStruct', defaultValue={})>
			<cfset local.AdvFormImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue(keyname='AdvFormImportStruct', value=local.AdvFormImportStruct)>
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareAdvanceFormulasImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.data = list(event=arguments.event, prepResult=local.prepResult)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareAdvanceFormulasImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.af_prepareAdvanceFormulasImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showAdvanceFormulasImportCompareResults(siteID=arguments.paramStruct.siteID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/AdvanceFormulasImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showAdvanceFormulasImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewAdvanceFormulas = XMLSearch(arguments.strResult.importResultXML,"/import/newadvanceformula/formula")>
			<cfset local.strImportResult.arrUpdateAdvanceFormulas = XMLSearch(arguments.strResult.importResultXML,"/import/updateadvanceformula/formula")>
			<cfset local.strImportResult.arrRemoveAdvanceFormulas = XMLSearch(arguments.strResult.importResultXML,"/import/removeadvanceformula/formula")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importReport = generateAdvanceFormulasImportResultsReport(siteID=arguments.siteID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateAdvanceFormulasImportErrorReport(siteID=arguments.siteID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Advance Formulas Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelAdvanceFormulasImport(siteID=arguments.siteID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Advance Formulas Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Advance Formulas Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateAdvanceFormulasImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateAdvanceFormulas)>
			<cfquery name="local.qryImportFileUpdateAdvanceFormulas" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select AFID as syncAFID, afName, [uid], [datePart], [dateNum], adjustTerm, nextWeekday, weekNumber, isSystemOnly
				from dbo.sync_af_advanceFormulas
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryUpdateAdvanceFormulas" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT AFID, afName, [uid], [datePart], [dateNum], adjustTerm, nextWeekday, weekNumber, isSystemOnly
				FROM dbo.af_advanceFormulas
				WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND [uid] in (#listQualify(valueList(local.qryImportFileUpdateAdvanceFormulas.uid), "'")#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateAdvanceFormulasImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportAdvanceFormulas" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="500">

		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.AdvFormImportStruct = application.mcCacheManager.sessionGetValue(keyname='AdvFormImportStruct', defaultValue={})>
		
		<cfif NOT isStruct(local.AdvFormImportStruct) OR NOT structKeyExists(local.AdvFormImportStruct,local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Advancement Formulas. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="af_importAdvanceFormulas" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Advancement Formulas file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from session --->
			<cfset StructDelete(local.AdvFormImportStruct, local.threadID)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='AdvFormImportStruct', value=local.AdvFormImportStruct)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelAdvanceFormulasImport" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			DELETE FROM dbo.sync_af_advanceFormulas 
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		
		<cftry>
			<cfset local.AdvFormImportStruct = application.mcCacheManager.sessionGetValue(keyname='AdvFormImportStruct', defaultValue={})>
			<cfif isStruct(local.AdvFormImportStruct) and structKeyExists(local.AdvFormImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.AdvFormImportStruct[arguments.reportuid].folderPath & "/AdvanceFormulasImportReport.html">
				<cfset local.returnStruct.reportOutput = "">

				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAdvanceFormulaDateRangesforAFID" access="public" output="false" returntype="struct">
		<cfargument name="startDate" type="string" required="true">
		<cfargument name="endDate" type="string" required="true">
		<cfargument name="afid" type="numeric" required="true">
		<cfargument name="numberOfSets" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "arrdateranges":[] }>

		<cfif NOT len(arguments.startDate) OR NOT len(arguments.endDate) OR arguments.numberOfSets LTE 0>
			<cfset local.returnStruct.success = false>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset local.startDate = ParseDateTime(replace(arguments.startDate,' - ',' '))>
		<cfset local.endDate = ParseDateTime(replace(arguments.endDate,' - ',' '))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.arrdateranges" returntype="array">
			SET NOCOUNT ON;

			DECLARE @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), @nextWeekday int, @weekNumber varchar(4), 
				@startDate datetime, @endDate datetime;
			SET @startDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startDate#">;
			SET @endDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endDate#">;

			SELECT @datePart = [datePart], @dateNum = [dateNum], @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
			FROM dbo.af_advanceFormulas
			WHERE AFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afid#">;

			WITH dateRanges as (
				select 0 as rowID, @startDate as startDate, @endDate as endDate
					union all
				select rowID + 1 as rowID, 
					dbo.fn_af_getAFDate(@startDate, @datePart, (rowID + 1) * @dateNum, @adjustTerm, @nextWeekday, @weekNumber) as startDate,
					dbo.fn_af_getAFDate(@endDate, @datePart, (rowID + 1) * @dateNum, @adjustTerm, @nextWeekday, @weekNumber) as endDate
				from dateRanges
				where rowID < <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.numberOfSets#">
			)
			SELECT startdate, enddate
			FROM dateRanges
			WHERE rowID > 0;
		</cfquery>

		<cfset local.returnStruct.arrdateranges.each(
			function(thisRow) {
				arguments.thisRow.startdate = dateTimeFormat(arguments.thisRow.startdate,"m/d/yyyy h:nn tt");
				arguments.thisRow.enddate = dateTimeFormat(arguments.thisRow.enddate,"m/d/yyyy h:nn tt");
			}
		)>
		
		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>