<cfsavecontent variable="local.swCatalogCommonCSS">
	<cfoutput>
	<style type="text/css">
	@import url("//cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css");
	@charset "utf-8";
	.swCatalog h1, .swCatalog h2, .swCatalog h3, .swCatalog h4, .swCatalog h5, .swCatalog h6 {line-height: 1 !important; margin: 10px 0 !important; border: none !important; padding: 0 !important; text-align:left !important;background: none !important;}
	.swCatalog h1 {font-size:2.375em !important;text-transform: none !important;}
	.swCatalog h2 {font-size:1.75em !important;text-transform: none !important;}
	.swCatalog h3 {font-size:1.60em !important;text-transform: none !important;}
	.swCatalog h4 {font-size:1.3em !important;text-transform: none !important;}
	.swCatalog h5 {font-size:1.1em !important;text-transform: none !important;}
	.swCatalog h6 {font-size:.95em !important;text-transform: none !important;}
	.swCatalog .btn {min-width:inherit;}
	.swCatalog *, .swCatalog input[type="search"], .swCatalog input[type="text"] { -moz-box-sizing: border-box !important; -ms-box-sizing: border-box !important; -o-box-sizing: border-box !important; -webkit-box-sizing: border-box !important; box-sizing: border-box !important; }
	.swCatalog h2:after, .swCatalog h3:after, .swCatalog h4:after, .swCatalog h5:after {content:none !important;}
	.swCatalog .nav {list-style:none;}
	.swCatalog .nav li {border:none;}
	.swCatalog p {text-align:unset !important; margin: 0 0 10px; padding:0; width:auto; display:block;}
	.swCatalog .muted {color: ##999!important;}
	.swCatalog .small, .swCatalog small { font-size: 85%; }
	.swCatalog fieldset {border:none !important;}
	.swCatalog [data-toggle="dropdown"] {display: inherit !important;}
	.swCatalog .nav i {display: inherit;}
	.swCatalog .dropdown-menu {width: inherit;}

	.swCatalog input { outline: none; }
	.swCatalog img { max-width: 100%; }
	.swCatalog a { text-decoration: none; }
	.swCatalog a:hover, .swCatalog a:focus { text-decoration: none; }
	.swCatalog .sw-d-none {display:none !important;}
	.swCatalog .sw-d-flex {display:flex !important; align-items:center;}
	.swCatalog .sw-d-inline-flex {display:inline-flex !important;}
	.swCatalog .sw-d-inline-block {display:inline-block !important;}
	.swCatalog .sw-text-light {color:##fff !important;}
	.swCatalog .sw-text-danger {color:##f83245!important}
	.swCatalog .text-light-grey { color:##d6d5d5; }
	.swCatalog .swRed { color:red !important; }
	.swCatalog .text-center {text-align:center !important;}
	.swCatalog .sw-w-100 {width:100% !important;}
	.swCatalog .sw-h-100 {height:100% !important;}
	.swCatalog .sw-mh-none {max-height:none !important;}
	.swCatalog .mr-auto {margin-right: auto!important;}
	.swCatalog .ml-auto {margin-left: auto!important;}
	.swCatalog .sw-align-top {vertical-align:top;}
	.swCatalog .sw-bg-grey {background-color: ##dddddd !important;}
	.swCatalog .sw-pt-0 {padding-top:0!important;}
	.swCatalog .sw-pr-0 {padding-right:0!important;}
	.swCatalog .sw-pl-3 {padding-left: 1em!important;}	
	.swCatalog .sw-mt-0 {margin-top:0!important;}
	.swCatalog .sw-mt-2 {margin-top:.5em!important;}
	.swCatalog .sw-mt-3 {margin-top:1em!important;}
	.swCatalog .sw-mt-4 {margin-top:1.5em!important;}
	.swCatalog .sw-mt-5 {margin-top:2em!important;}
	.swCatalog .sw-mb-0 {margin-bottom:0!important;}
	.swCatalog .sw-mb-1 {margin-bottom:.25em!important;}
	.swCatalog .sw-mb-2 {margin-bottom:.5em!important;}
	.swCatalog .sw-mb-3 {margin-bottom:1em!important;}
	.swCatalog .sw-mb-4 {margin-bottom:1.5em!important;}
	.swCatalog .sw-mb-5 {margin-bottom:2em!important;}
	.swCatalog .sw-ml-2 {margin-left:.5em!important;}
	.swCatalog .sw-mr-1 {margin-right:.25em!important;}
	.swCatalog .sw-mr-2 {margin-right:.5em!important;}
	.swCatalog .dropdown-menu > li > a { background:inherit; }

	/*Typography*/
	.swCatalog .swPrimary { color: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important; }
	.swCatalog .swPrimaryHover:hover {color: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important;}
	.swCatalog .swPrimaryBkgd { background: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important; }
	.swCatalog .swPrimaryBkgdHover:hover {background: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important;}
	.swCatalog .swPrimaryBorder { border-color:#attributes.data.semWeb.qrySWP.brandPrimaryColor# !important; border: 2px solid #attributes.data.semWeb.qrySWP.brandPrimaryColor#; }
	.swCatalog .swPrimaryBorderHover:hover {border: 1px solid #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important;}
	.swCatalog .mcEvent { color: #attributes.data.semWeb.qrySWP.brandConfColor# !important; }
	.swCatalog .mcEventBorder { border-color: #attributes.data.semWeb.qrySWP.brandConfColor# !important; border: 2px solid #attributes.data.semWeb.qrySWP.brandConfColor#; }
	.swCatalog .swWebinar { color: #attributes.data.semWeb.qrySWP.brandSWLColor# !important; }
	.swCatalog .swWebinarBorder { border-color: #attributes.data.semWeb.qrySWP.brandSWLColor# !important; border: 2px solid #attributes.data.semWeb.qrySWP.brandSWLColor#; }
	.swCatalog .swOnDemand { color:#attributes.data.semWeb.qrySWP.brandSWODColor# !important; }
	.swCatalog .swOnDemandBorder { border-color:#attributes.data.semWeb.qrySWP.brandSWODColor# !important; border: 2px solid#attributes.data.semWeb.qrySWP.brandSWODColor#; }
	.swCatalog .swBundle { color: #attributes.data.semWeb.qrySWP.brandBundleColor# !important; }
	.swCatalog .swBundleBorder { border-color: #attributes.data.semWeb.qrySWP.brandBundleColor# !important; border: 2px solid #attributes.data.semWeb.qrySWP.brandBundleColor#; }
	.swCatalog .swAttended { color: #attributes.data.semWeb.qrySWP.brandAttendedColor# !important; }
	.swCatalog .swAttendedHover:hover { color: #attributes.data.semWeb.qrySWP.brandAttendedColor# !important; }
	.swCatalog .swWhite { color: ##fff !important; }
	.swCatalog .swWhiteHoverText:hover { color: ##fff !important; }
	.swCatalog .boldHoverText:hover {font-weight: bold !important;}
	.swCatalog .textUnderline { text-decoration: underline; }
	.swCatalog .flex { display: flex; }
	.swCatalog .text-nowrap { white-space: nowrap; }
	.swCatalog .sw-font-weight-bold {font-weight:700!important;}

	/*Header*/
	##swHeaderDiv .nav-tabs{border-bottom: 0px;margin: 10px 0px;}
	##swHeaderDiv .nav-tabs > li > a,
	##swHeaderDiv .nav-tabs > li > a:hover{border: 0px;background: transparent !important;}
	##swHeaderDiv hr{margin: 0px 0 10px}
	##swHeaderDiv .nav-tabs > li > a .bi{font-size: 21px;}
	##swHeaderDiv .alpha-sort .dropdown-menu {top:100% !important;display:none !important;}
	##swHeaderDiv .alpha-sort.open > .dropdown-menu {display:block !important;}
	##swHeaderDiv .nav-tabs > li > a.muted { cursor:default !important; }

	/*Accordion*/
	.swCatalog .accordion > .accordion-group > .accordion-heading > .accordion-toggle > i.accordion-toggle-icon { float: right; font-size: 15px; transform: rotate(180deg); }
	.swCatalog .accordion > .accordion-group > .accordion-heading > .accordion-toggle.collapsed > i.accordion-toggle-icon {transform: rotate(0deg);}
	.swCatalog .accordion .nestedCollapse > .accordion-group > .accordion-heading .accordion-toggle > i.accordion-toggle-icon { font-size: 15px; transform: rotate(90deg); margin-right: 10px;}
	.swCatalog .accordion .nestedCollapse > .accordion-group > .accordion-heading .accordion-toggle.collapsed > i.accordion-toggle-icon {transform: rotate(0deg);}

	/*Featured Program Thumbnails*/
	.swCatalog .featuredPrograms .thumbnail{padding: 15px; border: 1px solid ##ddd;}
	.swCatalog .featuredPrograms .thumbnails .thumbnail {margin:0;}
	.swCatalog .featuredPrograms .thumbnail:hover{box-shadow: none !important;}
	.swCatalog .featuredPrograms .thumbnail img{max-width: 140px!important;width:100%;float:none;margin:0 auto 15px auto!important;}
	.swCatalog .featuredPrograms .thumbnail h3{line-height: 26px;min-height: 80px;margin-bottom: 10px;text-transform: none;}
	.swCatalog .featuredPrograms .thumbnail span{text-transform: uppercase;font-size: 1em;}
	.swCatalog .featuredPrograms .thumbnail span:last-child {text-transform: none;font-size: 16px;}
	.swCatalog .featuredPrograms .thumbnail p:last-child {margin-bottom:0;}
	.swCatalog .featuredPrograms .thumbnail span .bi{margin-right: 10px}
	.swCatalog .featuredPrograms .thumbnail, .swCatalog .featuredPrograms .thumbnail:hover {text-decoration:none !important; background:transparent !important;}
	.swCatalog .featuredPrograms .thumbnails .span3:nth-child(n+5) {margin-top:15px!important;}
	.swCatalog .featuredPrograms .thumbnails .span3:nth-child(4n+1) {margin-left:0!important;}
	.swCatalog .featuredPrograms .thumbnails .span4:nth-child(n+4) {margin-top:15px!important;}
	.swCatalog .featuredPrograms .thumbnails .span4:nth-child(3n+1) {margin-left:0!important;}
	
	/*Jump To Tab List */
	.swCatalog ul.swCatalogTabList { margin: 0!important; padding: 0!important; display: flex; align-items: center; flex-wrap: wrap; }
	.swCatalog ul.swCatalogTabList li { list-style: none!important; line-height: normal; padding: 0px 12px; border-left: 1px solid ##e0e1e2; font-size: 1em; }
	.swCatalog ul.swCatalogTabList li:first-child { padding-left: 0px; border-left: 0; }
	.swCatalog ul.swCatalogTabList .active { font-weight: bold; }
	.swCatalog ul.swCatalogTabList a::before {display: block; content: attr(data-label); font-weight: bold; height: 0; overflow: hidden; visibility: hidden; } /* prevents element shifting when made bold on hover */

	.swCatalog .browse [class^="bi-"]::before, .swCatalog .browse [class*=" bi-"]::before {line-height:1.2em;}
	.swCatalog .table-striped tbody>tr:nth-child(odd)>td, .table-striped tbody>tr:nth-child(odd)>th { background-color: ##f3f2f3!important; }

	/*icon badge*/
	.swCatalog i.sw_savedprogramscounticon.swIconBadge:after {content:attr(data-swsavedprogramscount);font-size: 0.75em;color:##000;}
	.swCatalog i.sw_regcartcounticon.swIconBadge:after {content:attr(data-swregcartcount);font-size: 0.75em;}

	/*autocomplete*/
	.ui-state-active {background: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important; border-color: ##9f9f9f !important;}
	.ui-menu-item .ui-menu-item-wrapper.ui-state-active {background: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important; border-color: ##9f9f9f !important;} 

	@media print {
		.swCatalog .sw-d-print-none { display: none !important; }
	}

	@media (min-width: 768px) {
		.swCatalog .sw-d-md-flex {display:flex !important;}
		.swCatalog .sw-flex-md-wrap {flex-wrap:wrap !important;}
	}
	@media only screen and (max-width:767px) {
	 	.swCatalog .alpha-sort{float: right;margin-top: 20px;text-align: right;}
	 	.swCatalog .alpha-sort { position: relative; }
	 	.swCatalog .alpha-sort .dropdown-toggle {display:inline !important;}
	 	.swCatalog .alpha-sort .dropdown-toggle i{font-size: 18px;}
		.swCatalog .alpha-sort .dropdown-menu { left: auto; right: 0; border-radius: 0px; background: ##000; text-align: left; border: 0px; margin: 5px 0px; position:absolute; max-width: 290px; }
		.swCatalog .alpha-sort ul { list-style: none!important; }
		.swCatalog .alpha-sort ul li { color: ##fff; font-size: 14px; font-style: italic; padding: 5px 15px; }
		.swCatalog .alpha-sort ul li a { color: ##fff; padding: 0px 0px; font-style: initial; font-weight: 500; margin: -5px -15px; padding: 5px 15px; background: ##000;}
		.swCatalog .alpha-sort ul li a i {margin-right: 10px;}
		.swCatalog .alpha-sort ul li a:hover { background: ##333640; }
		.swCatalog .alpha-sort .dropdown-toggle::after { content: ''; display: block; width: 10px; height: 10px; position: absolute; right: 10px; transition:300ms all; transform: rotate(360deg) scale(0); border-style:none; }
		.swCatalog .alpha-sort .drop-icon { transition:300ms all; transform: rotate(0deg) scale(1);}
		.swCatalog .alpha-sort.open .drop-icon { opacity:0; transition:300ms all; transform: rotate(360deg) scale(0);}
		.swCatalog .alpha-sort.open .dropdown-toggle::after { content: "\f5ee"; font-family:'bootstrap-icons'; display:inline-block; font-size:18px; transition:300ms all; transform:rotate(360deg) scale(1); margin:0; }
		.swCatalog .alpha-sort .dropdown-menu > li > a { white-space: nowrap !important; }
		.swCatalog .hidden-phone {display:none!important}
	}

	<!--- Catalog Landing Page CSS : Start --->
	/*middle content*/
	.swCatalogLanding .swCatalogMiddleSec{margin: 50px 0 50px}
	.swCatalogLanding .swCatalogMiddleSec .span6{overflow-y: hidden;}
	.swCatalogLanding .swSearchBox{padding: 30px 30px;display: inline-block;width: 100%;min-height: 100%;}
	.swCatalogLanding .swSearchBox p .lead{color: ##fff;}
	.swCatalogLanding .browse a{padding: 7px 10px;display: inline-block;width: 100%;text-align: center;max-width: 400px;text-transform: uppercase;margin: 0 0 10px;font-size: 1em;font-weight: 400;font-style: normal;white-space: nowrap;line-height:20px;}
	.swCatalogLanding .browse a:last-child{margin-bottom: 0px;}
	.swCatalogLanding .browse a .bi{margin: 0 10px}
	.swCatalogLanding .history{align-self: center;}
	.swCatalogLanding .history .historyIcon{font-size:26px;margin:5px 0;display: block;}
	.swCatalogLanding .history .historyText{font-size:1em;text-transform:uppercase;font-weight:bold;display: block;}

	.swCatalogLanding .featuredPrograms .thumbnails{margin-bottom: 30px!important;}
	.swCatalogLanding .featuredPrograms .thumbnails.threeColumn .thumbnail{padding: 15px 30px}
	.swCatalogLanding .featuredPrograms .thumbnails.threeColumn h3{min-height: 60px;}
	.swCatalogLanding .featuredPrograms .thumbnails.twoColumn .thumbnail{text-align: left;min-height: 160px;position: relative; padding-left: 160px;}
	.swCatalogLanding .featuredPrograms .thumbnails.twoColumn .thumbnail img{max-width: 120px;max-width: 140px!important;width:100%;position: absolute;top: 10px;left: 10px;}
	.swCatalogLanding .featuredPrograms .thumbnails.twoColumn .thumbnail h3{min-height: auto;}

	.swSearchBoxInputFieldFlex {
		border-bottom: 1px solid ##fff;    
		display: flex;
		flex-direction: row;
		margin-bottom: 15px;
	}

	.swSearchBoxInputFieldFlex .btn {
		box-shadow:none;
		-webkit-box-shadow:none; 
		-moz-box-shadow:none; 
		border:0 !important;
		padding:0 !important;
		background: none !important;
		color: ##fff;
		font-size: 18px !important;
	}

	.swSearchBoxInputFieldFlex > input {
		background-color: transparent;
		border: none;
		color: ##fff;
		flex: 1;
	}
	.swSearchBoxInputFieldFlex > input:focus {
		outline: none;
		box-shadow:none;
		-webkit-box-shadow:none; 
		-moz-box-shadow:none; 
	}


	/*custom checkbox*/
	.swCatalogLanding .swcustom-control {position:relative; display:block; min-height:1.425em; padding-left:2.1em; margin-bottom:0.5em; }
	.swCatalogLanding .swcustom-control-input {position:absolute; z-index:-1; opacity:0; }
	.swCatalogLanding .swcustom-control-input:focus~.swcustom-control-label:before { box-shadow:0 .313em .719em rgba(122,123,151,.1),0 .156em .125em rgba(122,123,151,.06); }
	.swCatalogLanding .swcustom-control-input:focus:not(:checked)~.swcustom-control-label:before { border-color:##9297da; }
	.swCatalogLanding .swcustom-control-label { position:relative; margin-bottom:0; vertical-align:top }
	.swCatalogLanding .swcustom-control-label:before { pointer-events:none; background-color:##fff; border:1px solid ##d1d2db; }
	.swCatalogLanding .swcustom-control-label:after, .swCatalogLanding .swcustom-control-label:before { position:absolute; top:.0625em; left:-1.5em; display:block; width:0.9em; height:0.9em; content:"" }
	.swCatalogLanding .swcustom-control-label:after { background:no-repeat 50%/50% 50%; }
	.swCatalogLanding .swcustom-checkbox .swcustom-control-label:before { border-radius:.29em; }
	.swCatalogLanding .swcustom-checkbox .swcustom-control-input:checked~.swcustom-control-label:after {
		content: "\2713"; color:#attributes.data.semWeb.qrySWP.brandPrimaryColor#; text-align:center; font-weight:bold;
	}

	@media only screen and (max-width:1199px) {
		.swCatalogLanding .swCatalogMiddleSec{margin: 30px 0 30px}
		.swCatalogLanding .swSearchBox p .lead{font-size: 22px}
		.swCatalogLanding .browse{width: min-content;}
	}
	@media only screen and (max-width:991px) {
	 	.swCatalogLanding ##swHeaderDiv {position:static !important;}
	 	.swCatalogLanding .browse h4 a .bi {margin: 0 3px;}
	 	.swCatalogLanding .thumbnail img,
	 	.swCatalogLanding .thumbnails.twoColumn .thumbnail img{max-width: 80px}
	 	.swCatalogLanding .thumbnails.twoColumn .thumbnail{min-height: 120px;padding-left: 120px;}
	}
	@media only screen and (max-width:979px) {
		.swCatalogLanding .browse a{min-width: 100%;max-width: 100%;}
	}
	@media only screen and (max-width:767px) {
		.swCatalogLanding .browse{width: initial;}
	 	.swCatalogLanding .container{padding: 0 15px}
		.swCatalogLanding ##swHeaderDiv h2{float: left;max-width: calc(100% - 25px);}
	 	.swCatalogLanding .swCatalogMiddleSec .container{padding: 0 0px}
	 	.swCatalogLanding .swCatalogMiddleSec .browse{padding: 0 15px}
	 	.swCatalogLanding .swCatalogMiddleSec{margin: 30px 0px}
	 	.swCatalogLanding .swCatalogMiddleSec .flex{display: block;}
		.swCatalogLanding .swCatalogMiddleSec h3{text-align: center;}
		.swCatalogLanding .swSearchBox p .lead{text-align: left;}
		.swCatalogLanding .browse h4{text-align: center;}
		.swCatalogLanding .swCatalogThumbBox h3{text-align: center;}
		.swCatalogLanding .thumbnail h3{min-height: auto}
		.swCatalogLanding .thumbnail{margin-bottom: 20px !important;}
		.swCatalogLanding .thumbnails.twoColumn .thumbnail{padding: 15px;text-align: center !important;}
		.swCatalogLanding .thumbnails.twoColumn .thumbnail img{position: static;}
		.swCatalogLanding .thumbnails [class*="span"]:last-child a.thumbnail{margin-bottom: 0px}
		.swCatalogLanding .thumbnails {margin-bottom: 20px;}
		.swCatalogLanding .swSearchBox{padding: 15px 15px}
		.swCatalogLanding .browse, .swCatalogLanding .history{margin-top: 30px}
		.swCatalogLanding .history{ padding: 15px 15px; }
		.swCatalogLanding .swCatalogThumbBox h5{text-align: center !important;}
	}
	@media only screen and (max-width:479px) {
		.swCatalogLanding .browse a{max-width: 100%;min-width: auto; font-size:14px;}
	}
	<!--- Catalog Landing Page CSS : End --->

	<!--- My History Page CSS : Start --->
	.swHistory ##swHeaderDiv h2 {margin-right:10px!important;}	
	.swHistory .viewCatalogBtn {display: block;text-align: center;font-size: 1.1em;padding: 5px 0px;border-radius: 2px;margin-bottom: 35px;box-shadow: none;}
	.swHistory .viewCatalogBtn i { margin-left: 10px; }
	.swHistory .featuredPrograms .thumbnails{margin-bottom: 15px!important;}

	.swHistory ##programsAccordion { margin-top: 35px; }
	.swHistory ##programsAccordion > .accordion-group >.accordion-heading, 
	.swHistory ##programsAccordion > .accordion-group >.accordion-heading > .accordion-toggle:focus { background:none !important; }
	.swHistory ##programsAccordion > .accordion-group > .accordion-heading > .accordion-toggle { padding-left: 0px; padding-bottom:15px; }
	.swHistory ##programsAccordion >.accordion-group { border: 0; border-radius: 0; margin-bottom: 20px; border-bottom: 1px solid ##e0e1e2; }
	.swHistory ##programsAccordion .accordion-inner { padding: 5px 0px; border-top: none; }

	.swHistory table.swCatalogPrgmsTbl { margin-bottom: 0; }
	.swHistory table.swCatalogPrgmsTbl th { text-transform: uppercase; }
	.swHistory table.swCatalogPrgmsTbl tbody tr { height:50px; }
	.swHistory table.swCatalogPrgmsTbl td.swCatalogPrgmStatus { min-width:12ch; }
	.swHistory table.swCatalogPrgmsTbl td.swCatalogPrgmActions, .swHistory table.swCatalogPrgmsTbl td.swCatalogPrgmStatus { vertical-align: middle; }
	.swHistory table.swCatalogPrgmsTbl .swCatalogPrgmActions a, .swHistory .swCatalogPrgmsWell .swCatalogPrgmActions a { color: inherit!important; }
	.swHistory table.swCatalogPrgmsTbl .swCatalogPrgmActions > i, .swHistory table.swCatalogPrgmsTbl .swCatalogPrgmActions > a { margin-right:5px; }
	.swHistory .swCatalogPrgmsWell .swCatalogPrgmActions b:first-child, .swHistory .swCatalogPrgmsWell .swCatalogPrgmActions .action:not(:last-child) { margin-right:5px; }
	.swHistory .swCatalogPrgmsWell .swCatalogPrgmActions .action i { margin-right: 3px; }
	
	.swHistory .dateFilterSection {margin-top:10px;}
	.swHistory .dateFilterLabel i {margin-left:5px;}
	.swHistory .swCatalogPrintBtn i {margin-right:5px;}
	.swHistory .swCatalogPrintBtn a {color:inherit;}
	.swHistory .swCatalogSearchKey { position: relative; }
	.swHistory .swCatalogSearchKey input { background-color: ##fff; height: 30px; width: 100%; padding-right: 40px; padding-left: 10px; border-color: ##e0e1e2; border-radius: 2px; box-shadow: none;}
	.swHistory .swCatalogSearchKey button { height: 30px; border: 0; box-shadow: none; position: absolute; right: 0; }
	.swHistory .swCatalogSearchKey input::placeholder {font-style: italic; color: ##B3B3B3;}
	.swHistory ##btnFilterMyHistory { text-shadow:none; }
	
	@media screen and (min-width: 766px){
		.swHistory .swCatalogPrgmsWell{ display:none; }
		.swHistory .swCatalogPrgmsTbl{ display:table; }
	}
	@media screen and (min-width: 992px) and (max-width: 1024px) {
		.swHistory .featuredPrograms .thumbnail img{ max-width: 100px; }
	}
	@media only screen and (min-width: 768px) and (max-width:991px) {
		.swHistory .featuredPrograms .thumbnail img{ max-width: 80px; }
	}
	@media screen and (max-width: 765px){
		.swHistory .swCatalogPrgmsWell{ display:block; }
		.swHistory .swCatalogPrgmsTbl{ display:none; }
	}
	@media screen and (max-width: 480px){
		.swHistory .swCatalogPrintBtn {display:block!important; float:none!important; margin-bottom:20px;}
	}
	<!--- My History Page CSS : End --->

	<!--- FAQ Page CSS : Start --->
	.swFAQ heaswHeaderDivder h2 {margin-right:10px!important;}
	.swFAQ .accordion .accordion-inner-faq a {color: #attributes.data.semWeb.qrySWP.brandPrimaryColor# !important;}
	.swFAQ .accordion.swFAQAccordion {margin-bottom:35px;}
	<!--- FAQ Page CSS : End --->

	<!--- Cart Page CSS : Start --->
	.swCatalogCart hswHeaderDiveader h2 {margin-right:10px!important;}	
	<!--- Cart Page CSS : End --->

	<!--- Program Details Page CSS : Start --->
	.swCatalogWrapper.swProgramDetails { margin-bottom:10px; }
	.swProgramDetails small { font-size: .85em; }
	.swProgramDetails .lead { font-size: 1.2em; }
	.swProgramDetails p { text-align: unset !important; }
	.swProgramDetails .flex { align-items: center;}
	.swProgramDetails img { max-width: 100%; }
	.swProgramDetails a { text-decoration: none; outline: none !important; }
	.swProgramDetails a:hover, a:focus { text-decoration: none }
	.swProgramDetails .swProgramDetailsHeader ul { margin: 0; padding: 0; list-style: none;}
	.swProgramDetails li { line-height: normal; }
	.swProgramDetails .mb-0 { margin-bottom: 0; }
	.swProgramDetails .alert { margin-bottom: 10px; }

	.swProgramDetails .swCatalogSeparator { padding: 10px 0px 0px; border-bottom: 1px solid ##e0e1e2; }
	.swProgramDetails .swCatalogToolBar a { padding-top: 0;padding-bottom: 0; }
	.swProgramDetails .expand { position: relative; max-height: 175px; overflow: hidden;}
	.swProgramDetails .expand::after { background: linear-gradient(rgba(255,255,255,0),rgba(255,255,255,.5),rgba(255,255,255,.9), rgb(255, 255, 255)); display: block; padding: 40px 0px; content: ''; position: absolute; bottom: 0; left: 0; width: 100%; }
	.swProgramDetails .expand > a { position: absolute; padding-left: 20px; z-index: 9; bottom: 0; }
	.swProgramDetails .expand > a::before { position: absolute; left: 1px; width: 0; height: 0; content: '\f229'; top: 1px; margin: 0 auto; font-family:'bootstrap-icons'; font-size: 14px; font-weight: normal; }		
	.swProgramDetails .expand.open { max-height: initial; padding-bottom: 10px; }
	.swProgramDetails .expand.open:after { display: none; }
	.swProgramDetails .expand.open > a::before { content: '\f235'; }
	.swProgramDetails .swCatalogTabList.jumpToList li a { font-size: 1.1em; }

	/* header */
	.swProgramDetails ##swHeaderDiv .flex { align-items: center; }
	.swProgramDetails ##swHeaderDiv .flex p { margin-bottom: 0; }
	.swProgramDetails .swProgramDetailsHeader .swCatalogImgBox { position: relative; }
	.swProgramDetails .swProgramDetailsHeader .swCatalogImgBox::after { position: absolute; left: 0; top: 0; width: 25%; height: 100%; background-image: linear-gradient(to right, rgba(0,0,0,.3), rgba(0,0,0,0)); content: ''; }
	.swProgramDetails .swProgramDetailsHeader .swCatalogIconBox { position: absolute; left: 10px; top: 10px; font-size: 30px; z-index: 9; }
	.swProgramDetails .swProgramDetailsHeader .swCatalogIconBox i { color: ##fff; }
	.swProgramDetails .swCatalogShareBox { justify-content: space-between; padding: 5px 0px; }
	.swProgramDetails .swCatalogShareBox p { font-style: italic; margin-right: 10px; }
	.swProgramDetails .swCatalogShareBox i { font-size: 36px; margin-left: 10px; }
	.swProgramDetails .swCatalogShareBox ul li { list-style: none !important; }
	.swProgramDetails .swProgramDetailsHeader-right { position: relative; }
	.swProgramDetails .swProgramDetailsHeader-right ul.lead li { position: relative; padding-left: 1.5em; margin-bottom: 10px; font-weight: normal; }
	.swProgramDetails .swProgramDetailsHeader-right ul.lead li::before { position: absolute; content: '\f26e'; top: -2px; left: 0px; font-family:'bootstrap-icons'; font-size: 1.5em; font-weight: normal; }
	.swProgramDetails .swProgramDetailsHeader-right span.lead { margin-top: 30px; display: block; }
	.swProgramDetails .swProgramDetailsHeader-right .sw-brand-label { text-transform: uppercase; font-size: 1.2em; padding: 5px 10px; position: absolute; top: -21px; right: 0; border-top: 0; }
	.swProgramDetails .swProgramDetailsHeader-right .sw-brand-label i { margin-right: 10px; }
	.swProgramDetails .swProgramDetailsHeader-img-section .swCatalogImgBox .overlay { position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%, -50%); font-size: 50px; color: ##fff; }
	.swProgramDetails .swProgramDetailsHeader-img-section .swCatalogImgBox .overlay i { color: ##ebeaeb;opacity: 0.7;font-size: 75px;}
	.swProgramDetails .swProgramDetailsHeader { padding-top: 15px; }
	.swProgramDetails .swProgramDetailsHeader-right .sw-brand-label { margin-top: -15px; }
	.swProgramDetails .swProgramDetailsHeader-right h1.swPrimary { padding-top:0.5em!important; }
	
	/* content left */
	.swProgramDetails .content .content-left-section { margin-left: 0px; padding-top: 15px; }
	.swProgramDetails .swCatalogSubBlocks { padding: 30px 0px 10px; }
	.swProgramDetails .swCatalogSubBlocks h3 { margin-bottom: 15px !important; }
	.swProgramDetails .swProgramCode { margin-top: 5px; }
	.swProgramDetails .presenter-block p { margin: 0; line-height: normal; }
	.swProgramDetails .presenter-block p.desc { font-size:.8em; line-height: 1.2; margin-top:3px; }
	.swProgramDetails .presenter-block { margin-bottom: 25px; }
	.swProgramDetails .swDetailCredit .swCatalogTabList li a { border-bottom: 2px solid transparent; }
	.swProgramDetails .swDetailCredit .swCatalogTabList .active a { border-bottom: 2px solid; }
	.swProgramDetails .swDetailCredit .tab-content { margin-top: 30px; }
	.swProgramDetails .swDetailCredit ##tab { margin-top: 30px; }
	.swProgramDetails .swDetailCredit .tab-pane { line-height: 20px; padding-left: 18px; border-left: 3px solid ##e0e1e2; }
	.swProgramDetails .presenter-block .firm { line-height:1em; }
	.swProgramDetails .swbProgramDetails ol##includedPrograms > li { margin-bottom:25px; }
	.swProgramDetails .swbProgramDetails ol##includedPrograms > li::marker{ font-size: 1.2em; }
	.swProgramDetails .swbProgramDetails ul.includedTitles { margin-left: 20px; }
	.swProgramDetails .swbProgramDetails ul.includedTitles > li { margin-bottom: 10px; }
	.swProgramDetails .swbProgramDetails ul.includedTitles li { line-height: 20px; }
	.swProgramDetails .swbProgramDetails .tab-content.creditdetailbox .tab-pane { padding-top:8px; }
	.swProgramDetails div.componentsAccordion div.icon { line-height: 1.5; }
	.swProgramDetails div.componentsAccordion div.icon i { margin-right: 5px; }
	.swProgramDetails div.componentsAccordion div.singleEntry { margin-left:15px; }
	.swProgramDetails .swbProgramDetails .includedPrograms ul.learningObjectives { margin: 0; padding: 0; list-style: none; }
	.swProgramDetails .swbProgramDetails .includedPrograms > .accordion-group > .accordion-heading > .accordion-toggle > h5 + i.accordion-toggle-icon { font-size:1.1875em !important; }
	.swProgramDetails .swbProgramDetails .includedPrograms ul.learningObjectives li { position: relative; padding-left: 20px; margin-bottom: 3px; font-weight: normal; }
	.swProgramDetails .swbProgramDetails .includedPrograms ul.learningObjectives li::before { position: absolute; content: '\f26e'; top: 2px; left: 0px; font-family:'bootstrap-icons'; }

	/* content right */
	.swProgramDetails .swCatalogContLightSection { padding-top: 15px; border-left: 1px solid ##ddd; padding-left: 30px; }
	.swProgramDetails .cart-btn { display: block; text-align: center; font-size: 1.1em; padding: 5px 0px; border-radius: 2px; margin-bottom: 15px; box-shadow: none; }
	.swProgramDetails .cart-btn i { margin-left: 10px; }
	.swProgramDetails .swCatalogContLightSection .swCatalogShareBox { justify-content: flex-start; }
	.swProgramDetails .swCatalogContLightSection .swCatalogShareBox i { font-size: 21px; }
	.swProgramDetails .swCatalogContLightSection .swCatalogShareBox p { font-size: 12px; }
	.swProgramDetails .swCatalogRightBlocks { position: relative; padding-left: 35px; margin-bottom: 20px; }
	.swProgramDetails .swCatalogRightBlocks h5 { margin-top: 0px !important; }
	.swProgramDetails .swCatalogRightBlocks h5 i { position: absolute; left: 0; top: 0; width: 25px; text-align: center; }
	.swProgramDetails .swCatalogRightBlocks .line-height-sm { line-height: .85em; margin-bottom: 3px; }
	.swProgramDetails .swDetailComponents .titleIncludedComponents { padding-left:10px; }
	.swProgramDetails .swDetailComponents .titleIncludedComponents .titleComponent { padding-bottom:10px; }

	/* other programs */
	.swProgramDetails .swOtherPrgms { border-top: 1px solid ##e0e1e2; padding: 25px 0px; margin-top: 60px; }
	.swProgramDetails .swOtherPrgms h4.muted { text-transform: uppercase; font-weight: normal; margin-bottom: 30px !important; }
	.swProgramDetails .swCatalogInnerPrgms { margin-bottom: 35px; }
	.swProgramDetails .swCatalogInnerPrgms h6 { margin-top: 0 !important; min-height: 45px; }
	.swProgramDetails .swCatalogInnerPrgms.no-image h6 { min-height: initial; }
	.swProgramDetails .swCatalogInnerPrgms p { margin-right: 15px !important; margin-top: 10px !important; }
	.swProgramDetails .swCatalogInnerPrgms p:first-child { text-transform: uppercase; }
	.swProgramDetails .swCatalogInnerPrgms p i { margin-right: 5px; }
	.swProgramDetails .swOtherPrgms h4 { font-weight: 500; }

	/* no image styles */
	.swProgramDetails .swProgramDetailsHeader-right h1 .bi.bi-heart, .swProgramDetails .swProgramDetailsHeader-right h1 .bi.bi-heart-fill { font-size: 25px; vertical-align: middle; margin-left: 10px; }
	.swProgramDetails .swProgramDetailsHeader-right h1 .bi.play-circle { font-size: 25px; vertical-align: middle; }
	.swProgramDetails .swCatalogInnerPrgms.no-image p { margin-top: 0px; }

	/* speaker view modal */
	.swProgramDetails ##swSpeakerViewModal .modal-header h3{margin-right:15px!important;}
	.swProgramDetails ##swSpeakerViewModal .close-btn { margin-top: 5px; font-size: 26px; outline: none; font-family: helvetica, sans-serif; opacity:.4; font-weight: 100;}
	.swProgramDetails ##swSpeakerViewModal .modal-body.loading { background:url('/assets/common/images/progress.gif') center center no-repeat; }

	@media only screen and (max-width:1199px) {
		.swProgramDetails .swCatalogShareBox p { margin-right: 0; font-size: 12px; line-height: 14px; }
	}
	@media only screen and (max-width:991px) {
		.swProgramDetails .swCatalogToolBar h3 { font-size: 19px; }
		.swProgramDetails .swProgramDetailsHeader-right .sw-brand-label { font-size: 14px; padding: 3px 10px; }
		.swProgramDetails .swCatalogTabList li { padding: 0px 10px; margin-bottom: 5px; }
		.swProgramDetails .swCatalogContLightSection { padding-left: 15px; }
		.swProgramDetails .swProgramDetailsHeader .swProgramDetailsHeader-img-section .swCatalogShareBox { display: block; float: none; }
		.swProgramDetails .swCatalogShareBox i { font-size: 20px; margin-left: 5px; }
		.swProgramDetails .swProgramDetailsHeader .swCatalogIconBox { font-size: 24px; }
		.swProgramDetails .swCatalogInnerPrgms .flex { display: block; }
		.swProgramDetails .swCatalogInnerPrgms .flex .muted { margin-top: 0; }
	}
	@media only screen and (max-width:767px) {
		.swProgramDetails .alpha-sort{margin-top: 0;}
		.swProgramDetails .presenter-block .span3, .swCatalogInnerPrgms .span3 { width: 23.404255319148934%; float: left; }
		.swProgramDetails .presenter-block .span9, .swCatalogInnerPrgms .span9 { width: 74.46808510638297%; float: left; padding-left: 15px; }
		.swProgramDetails .swProgramDetailsHeader .swCatalogShareBox { display: block; float: none; }
		.swProgramDetails .swCatalogShareBox i { font-size: 28px; margin: 5px 3px 0 0; }
		.swProgramDetails .swProgramDetailsHeader .swCatalogImgBox { margin-top: 10px; }
		.swProgramDetails .swProgramDetailsHeader-right span.lead { margin-top: 15px; }
		.swProgramDetails .swProgramDetailsHeader { padding-top: 30px; }
		.swProgramDetails .swCatalogContLightSection { padding-left: 0; border-left: 0px; }
		.swProgramDetails .swCatalogRightBlocks { background: ##f3f2f3; padding: 15px 30px 5px 55px; margin: 0px -20px 0; }
		.swProgramDetails .swCatalogRightBlocks:last-child { padding: 15px 30px 30px 55px; margin: 0px -20px 30px; }
		.swProgramDetails .swCatalogRightBlocks:nth-child(3) { padding-top: 30px; }
		.swProgramDetails .swCatalogRightBlocks:nth-child(3) h5 i { top: 30px; }
		.swProgramDetails .swCatalogRightBlocks h5 i { left: 15px; top: 15px; }
		.swProgramDetails .swCatalogContLightSection .swCatalogShareBox i { margin-left: 10px; }
		.swProgramDetails .swCatalogSubBlocks { padding: 20px 0px 10px; }
		.swProgramDetails .swCatalogSubBlocks > .pull-right { position: relative; top: 10px; font-size: 12px; }
		.swProgramDetails .swCatalogTabList li { padding: 0px 8px; margin-bottom: 5px; }
		.swProgramDetails .swCatalogContLightSection .swCatalogTabList { margin-bottom: 30px; }
		.swProgramDetails .swOtherPrgms { padding: 25px 0px; margin-top: 30px; }
		.swProgramDetails .swCatalogRightBlocks p { margin: 0; }
		.swProgramDetails .swCatalogContLightSection .swCatalogShareBox { padding-bottom: 0; }
		.swProgramDetails .swCatalogInnerPrgms p { margin-top: 0px; }
		.swProgramDetails .swProgramDetailsHeader-right .sw-brand-label { margin-top: -20px; }
		.swProgramDetails .swCatalogInnerPrgms h6 { min-height: auto; }
		.swProgramDetails .swCatalogInnerPrgms .flex {display: flex;}
		.swProgramDetails .swDetailSponsors div.sponsorImage { margin-bottom:8px; }
	}
	@media only screen and (min-width:768px) {
		.swProgramDetails .swCatalogRightBlocks.copyToCalendar { margin-top:20px; }
		.swProgramDetails .swProgramDetailsHeader img { width: 100%; }
	}
	@media screen and (min-width: 992px) {
		.swProgramDetails ##swSpeakerViewModal.modal { width: 700px; margin-left: -350px;}
	}
	<!--- Program Details Page CSS : End --->

	<!--- Browse Programs Page CSS : Start --->
	.swBrowsePrograms.swCatalogWrapper {border:none !important;}
	.swBrowsePrograms ul, .swBrowsePrograms ul li { margin: 0; padding: 0; list-style: none !important; }
	.swBrowsePrograms .banner .owl-theme .owl-dots {position: absolute;margin: 0px;margin-top: 0px;left: 0;right: 0;top: auto;bottom: 15px;line-height: 1;}
	.swBrowsePrograms .swCatalogToolBarFrame p {padding-top: 8px!important; padding-bottom: 8px!important;}
	.swBrowsePrograms .swCatalogToolBarFrame .nav-tabs{border-bottom: 0px;margin: 0px 0px; text-transform: uppercase !important;}
	.swBrowsePrograms .swCatalogToolBarFrame {padding: 10px 0 7px 0; border-bottom: 1px solid ##e0e1e2;}
	.swBrowsePrograms .swCatalogToolBarFrame .nav-tabs > li > a,
	.swBrowsePrograms .swCatalogToolBarFrame .nav-tabs > li > a:hover{border: 0px;background: transparent!important;}
	.swBrowsePrograms .swCatalogToolBarFrame .nav-tabs > li > a.muted { cursor:default !important; }
	.swBrowsePrograms .swCatalogToolBarFrame .swCatalogToolBarFrameFormatIcon ul li a .bi{ margin-right: 15px; font-size: 17px; display:inline-block;vertical-align:bottom;}
	.swBrowsePrograms .swCatalogToolBarFrame .swFormatHead > li > a:hover { text-decoration: underline; }
	.swBrowsePrograms .swCatalogToolBarFrame ul li a i { font-size: 21px; }
	.swBrowsePrograms .swCatalogToolBarFrame .last { display: flex; justify-content: flex-end; align-items: center; }
	.swBrowsePrograms .swCatalogToolBarFrame .last .dropdown-menu i { font-size: 21px; margin-right: 15px; }
	.swBrowsePrograms .swCatalogToolBarFrame p { margin: 0px!important; }
	.swBrowsePrograms .swCatalogToolBarFrame .popover-icon i { margin-right: 0px; }
	.swBrowsePrograms .refine-btn{background: transparent;border: 0; box-shadow: none; text-shadow: none; border-color: none; border-radius: 2px; }
	.swBrowsePrograms .refine-btn .bi{margin-right: 10px}
	.swBrowsePrograms .refine-btn:focus{background: transparent;outline: none;}
	/* content */
	.swBrowsePrograms .swCatalogContentFrame {display: flex;width: 100%;position: relative;height: auto;}
	.swBrowsePrograms .swCatalogContentFrame > .span3 { border-right: 1px solid ##e0e1e2; border-left: 1px solid ##e0e1e2; border-bottom: 1px solid ##e0e1e2; background: ##f3f2f3;}
	/* Tile */
	.swBrowsePrograms .swCatalogTitleTopBox { display: flex; }
	.swBrowsePrograms .swCatalogTiles .swCatalogTileResult { padding: 10px 0px 0px; background: ##fff; border-bottom: 1px solid ##e0e1e2; }
	.swBrowsePrograms .swCatalogTiles .swCatalogTileResult:nth-last-child(2){border-bottom: 0px solid ##e0e1e2; }
	.swBrowsePrograms .swCatalogTileBox { position: relative; min-width: 185px; margin-bottom: 15px; }
	.swBrowsePrograms .swCatalogTileBox .swCatalogImgBox { position: relative; max-width: 185px;}
	.swBrowsePrograms .swCatalogTileBox .swCatalogImgBox .overlay { position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%, -50%); font-size: 50px; color: ##fff; }
	.swBrowsePrograms .swCatalogTileBox .swCatalogImgBox .overlay i { color: ##ebeaeb;opacity: 0.7;font-size: 75px;}
	.swBrowsePrograms .swCatalogTileBox .swCatalogImgBox:after { position: absolute; content: ''; background: url('/assets/common/images/seminarWeb/swTileBox.png'); width: 45px; height: 100%; top: 0; left: 0px; z-index: 0; pointer-events: none; }
	.swBrowsePrograms .swCatalogTileBox .swCatalogImgBox img { width: 185px; float:none; margin:0; }
	.swBrowsePrograms .swCatalogTileBox .swCatalogIconBox { position: absolute; top: 7px; left: 7px; }
	.swBrowsePrograms .swCatalogIconBox > ul { z-index: 1; position: relative; }
	.swBrowsePrograms .swCatalogIconBox > ul > li { padding: 5px 5px 5px 0px; position: relative; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > a { background: transparent; outline: none; -webkit-box-shadow: none; box-shadow: none; background-image: none; text-shadow:none; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > a > i { font-size: 21px; color: ##ebeaeb; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > a.swHeartIcon:hover > i { color: red!important; }
	.swBrowsePrograms .swCatalogIconBox > ul > li.open > a i.swAttendedHover { color: #attributes.data.semWeb.qrySWP.brandAttendedColor# !important; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > ul { position: absolute; top: 5px; left: 100%; background: ##000000; padding: 5px; text-align: center; min-width: auto; border-radius: 0px; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > ul > li { padding: 2px 0; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > ul > li i{font-size: 20px;}
	.swBrowsePrograms .swCatalogIconBox > ul > li > ul > li a { padding: 3px; }
	.swBrowsePrograms .swCatalogIconBox > ul > li > ul > li a:hover, .swCatalogIconBox > ul > li > ul > li a:focus { background-image: none; background-color: transparent; }
	.swBrowsePrograms .swCatalogTileBottomBox { position: relative; min-height: 40px; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a { text-align: center; position: relative; padding-bottom: 5px; padding-left: 20px; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a.swLearningObj { display: inline-block !important; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a.swSpeakersObj { display: inline-block !important; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn { display: inline-block !important; margin-right: 15px; }
	.swBrowsePrograms .swCatalogTileBottomBox ul { margin-bottom: 10px; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a::before { position: absolute; left: 0; width: 0; height: 0; content: '\f229'; top: 0px; margin: 0 auto; font-family:'bootstrap-icons'; font-weight: normal;}
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a.swSpeakersObj::before { content: '\1F464'; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open > a::before,
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open1 > a::before{ content: '\f235'; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open > a.swSpeakersObj::before,
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open1 > a.swSpeakersObj::before{ content: '\1F464'; }
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a::after { position: absolute; left: 0; right: 0; width: 0; height: 0; border-left: 35px solid transparent; border-right: 35px solid transparent; border-bottom: 27px solid ##f3f2f3; content: ""; top: 100%; margin: 0 auto; bottom: 0px; display: none; }
	.swBrowsePrograms .swCatalogTileBottomBox >ul> li:last-child { position: absolute; top: 0; right: 0; }
	.swBrowsePrograms .swCatalogTileBottomBox .btnBox { display: flex; text-transform: uppercase !important; }
	.swBrowsePrograms .swCatalogTileBottomBox .btnBox a:hover { text-decoration: underline; }
	.swBrowsePrograms .swCatalogTileBottomBox .btnBox .addcart { background: transparent; font-size: 15px; border-radius: 2px; padding: 5px 30px; margin: -10px 0px 0px 15px; border-left-width: 2px; border-right-width: 2px; border-top-width: 2px; border-bottom-width: 2px; }
	.swBrowsePrograms .swCatalogTileBottomBox .btnBox .addcart:hover { text-decoration: none; text-shadow: none;box-shadow: none; }
	.swBrowsePrograms .swCatalogTileBottomBox .btnBox .viewDetails { font-size: 15px; }
	.swBrowsePrograms .swCatalogPointBtn { text-align: center; }
	.swBrowsePrograms .swCatalogContentFrame.open .swCatalogPointBtn { text-align: left; }
	.swBrowsePrograms .swCatalogPoints { padding: 20px 25px; background: ##f3f2f3; display: none; }
	.swBrowsePrograms .swCatalogPointBtn.open .swCatalogPoints,
	.swBrowsePrograms .swCatalogPointBtn.open1 .swCatalogPoints{ display: block; margin-top: 15px;}
	.swBrowsePrograms .swCatalogPoints li { padding-left: 1.5em; position: relative; text-align: left; }
	.swBrowsePrograms .swCatalogPoints li.swLearningObjLabel, .swBrowsePrograms .swCatalogPoints li.swIncProgramLabel, .swBrowsePrograms .swCatalogPoints li.swSpeakersLabel {font-weight:bold !important;}
	.swBrowsePrograms .swCatalogPoints li.swIncProgramLabel {padding-top:10px !important;}
	.swBrowsePrograms .swCatalogPoints li.swSpeakersLabel {padding-top:10px !important;}
	.swBrowsePrograms .swCatalogPoints li.swLearningObjPoint::after { position: absolute; content: '\f26e'; top: 2px; left: 0px; font-family:'bootstrap-icons'; font-weight: normal;font-size: 1.5em;}
	.swBrowsePrograms .swCatalogPoints li.swIncProgramPoint::after { position: absolute; content: '\f26e'; top: 2px; left: 0px; font-family:'bootstrap-icons'; font-weight: normal;font-size: 1.5em;}
	.swBrowsePrograms .swCatalogPoints li.swSpeakersPoint::after { position: absolute; content: '\1F464'; top: 2px; left: 0px; font-family:'bootstrap-icons'; font-weight: normal;font-size: 1.5em;}
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open a::after,
	.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open1 a::after{ display: block; }
	.swBrowsePrograms .swCatalogContentFrame.xs-open .swCatalogTileBox{max-width: 185px;}
	/* Middlie Column */
	.swBrowsePrograms .swCatalogMiddleColumn span.featuredLabel { text-transform: uppercase !important; padding: 2px 10px; margin-bottom: 5px; }
	.swBrowsePrograms .swCatalogMiddleColumn a.visible-phone{font-size: 17px; text-transform: uppercase !important;}
	.swBrowsePrograms .swCatalogMiddleColumnWithoutPhotos{padding-left: 30px;position: relative;}
	.swBrowsePrograms .swCatalogMiddleColumnWithoutPhotos .swCatalogIconBox{position: absolute;left: 0;top: 0px;}
	.swBrowsePrograms h2.prgmSubtitle { line-height: 85%!important; }
	/* Right Column */
	.swBrowsePrograms .swCatalogProgramContent > p.swFormatLabel {margin: 0 0 5px 0; text-transform: uppercase !important; font-weight: normal; font-size: 17px;}
	.swBrowsePrograms .swCatalogProgramContent i { margin-right: 10px; }
	.swBrowsePrograms .swCatalogProgramContent .attended i{margin-right: 5px}
	.swBrowsePrograms .swCatalogProgramContent p{line-height: 1.2;padding-bottom:0px!important;}
	.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard { display: inline-block; border-radius: 4px;width: 40px; position: relative; cursor: default; padding: 5px 5px 15px 5px; }
	.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard:after { position: absolute; content: ''; width: 0; border-left: 18px solid transparent;
		border-right: 18px solid transparent; border-bottom: 10px solid ##fff; left: 2px; top: auto; bottom: 0px; right: 5px;margin: 0 auto; display: block;}
	.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard h6 { color: ##fff !important; margin: 0 !important; font-size: 8px !important; font-style: italic !important; line-height: 1 !important; text-align:center !important; }
	.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard h3 { color: ##fff !important; margin: 0 !important; line-height: 1 !important; font-size: 24px !important; font-weight: 600 !important; text-align:center !important; }
	.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard p { color: ##fff !important; margin: 0 !important; font-size: 8px !important; line-height: 1 !important; text-align:center !important; }
	
	/*****left side bar css****/
	.swBrowsePrograms .swCatalogSideBarTop { padding: 15px; }
	.swBrowsePrograms .swCatalogSideBarTop ul.sw_filterSummary { margin-bottom: 15px; }
	.swBrowsePrograms .swCatalogSideBarTop ul.sw_filterSummary li span.badge { border-radius: 2px; cursor:pointer; }
	.swBrowsePrograms .swCatalogSideBarTop ul.sw_filterSummary li span.badge span { font-size: 11px; }
	.swBrowsePrograms .swCatalogSideBarTop ul.sw_filterSummary li { margin: 0px 5px 0 0; display: inline-block; }
	.swBrowsePrograms .swCatalogSideBarTop ul.sw_filterSummary li span i { margin-right: 5px; font-size: 10px; }
	.swBrowsePrograms .swCatalogSideBarTop .clear { margin-bottom: 15px; display: inline-block; }
	.swBrowsePrograms .accordion-inner .clear.muted{margin-bottom: 0px;}
	.swBrowsePrograms .swCatalogSideBarTop .swCatalogSearchKey { position: relative; }
	.swBrowsePrograms .swCatalogSideBarTop .swCatalogSearchKey input { background-color: ##fff; height: 35px; width: 100%; padding-right: 40px; padding-left: 10px; border-color: ##e0e1e2; border-radius: 2px; box-shadow: none;}
	.swBrowsePrograms .swCatalogSearchKey button { height: 35px; background: transparent; border: 0; box-shadow: none; position: absolute; right: 0; }
	.swBrowsePrograms .swCatalogSearchKey input::placeholder {font-style: italic; color: ##B3B3B3;}

	.swBrowsePrograms .collapseSections .accordion-toggle {text-transform: uppercase !important; outline: none; padding: 15px 15px 15px 15px; color: inherit;}
	.swBrowsePrograms .collapseSections .parentAccordion > .accordion-group > .accordion-heading > .accordion-toggle > i { float: right; font-size: 15px; transform: rotate(180deg);}
	.swBrowsePrograms .collapseSections .parentAccordion > .accordion-group > .accordion-heading > .accordion-toggle.collapsed > i{transform: rotate(0deg);}
	.swBrowsePrograms .collapseSections .parentAccordion .nestedCollapse > .accordion-group > .accordion-heading > .accordion-toggle > i { font-size: 15px; transform: rotate(90deg);margin-right: 10px;}
	.swBrowsePrograms .collapseSections .parentAccordion .nestedCollapse > .accordion-group > .accordion-heading > .accordion-toggle.collapsed > i{transform: rotate(0deg);}
	.swBrowsePrograms .parentAccordion .swCatalogSideBarTop{padding: 0px;}
	.swBrowsePrograms .swFiltering .accordion-group >.accordion-heading, 
	.swBrowsePrograms .swFiltering .accordion-group >.accordion-heading .accordion-toggle:focus { background:none !important; }
	.swBrowsePrograms .collapseSections .accordion-group { border: 0; border-radius: 0; }
	.swBrowsePrograms .collapseSections .accordion-body .accordion-inner {border-top: 1px solid ##e0e1e2;border-bottom: 1px solid ##e0e1e2;}
	.swBrowsePrograms .collapseSections .accordion-body .accordion-inner select{ background-color: ##fff; height: 35px; width: 100%; padding-right: 40px; padding-left: 10px; border-color: ##e0e1e2; border-radius: 2px; box-shadow: none; }
	.swBrowsePrograms .collapseSections .accordion-inner { padding: 25px; border-top: none; border-bottom: 1px solid ##e0e1e2; }
	.swBrowsePrograms .accordion-inner label { margin-bottom: 5px; padding: 0; }
	.swBrowsePrograms .accordion-inner form {margin-bottom: 0;}
	.swBrowsePrograms .checkbox input { opacity: 0; visibility: hidden; width: 0; height: 0; }
	.swBrowsePrograms .accordion-inner label:last-child { margin-bottom: 0px; }
	.swBrowsePrograms .accordion-inner .checkbox span { display: inline-block; width: 12px; height: 12px; border: 1px solid ##82848a; border-radius: 2px; position: relative; margin-right: 20px; margin-bottom: 0; vertical-align: middle; }
	.swBrowsePrograms .accordion-inner .checkbox span:before { content: ''; display: inline-block; width: 3px; height: 8px; border-right: 2px solid ##fff; position: absolute; left: 3px; border-bottom: 2px solid ##fff; transform: rotate(45deg); top: -1px; opacity: 0; }
	.swBrowsePrograms .accordion-inner label input:checked + span { background-color: #attributes.data.semWeb.qrySWP.brandPrimaryColor#; }
	.swBrowsePrograms .accordion-inner label input:checked + span:before { opacity: 1; }
	.swBrowsePrograms .collapseSections .nestedCollapse .accordion-toggle {padding: 5px 15px 5px 0px}
	.swBrowsePrograms .swCatalogToolBar.second .muted { display: inline-block; font-style: italic; vertical-align: middle;}
	.swBrowsePrograms .swCatalogContentFrame > .span3 { width: 0px; display: none; position: relative; }
	.swBrowsePrograms .swCatalogContentFrame > .span9 { width: 100%; margin-left: 10px; }
	.swBrowsePrograms .swCatalogContentFrame.open > .span3 { width: 23.076923076923077%; }
	.swBrowsePrograms .swCatalogContentFrame.open > .span9 { width: 74.35897435897436%; margin-left: 2.564102564102564%; }
	.swBrowsePrograms .alpha-sort { position: relative; }
	.swBrowsePrograms .alpha-sort .dropdown-menu { left: auto; right: 0; border-radius: 0px; background: ##000; text-align: left; border: 0px; margin: 5px 0px; }
	.swBrowsePrograms .alpha-sort ul li { color: ##fff; font-size: 14px; font-style: italic; padding: 5px 15px;}
	.swBrowsePrograms .alpha-sort ul li a { color: ##fff; padding: 0px 0px; font-style: initial; font-weight: 500; margin: -5px -15px; padding: 5px 15px; }
	.swBrowsePrograms .alpha-sort ul li a:hover { background: ##333640; }
	.swBrowsePrograms .alpha-sort .dropdown-menu::before { content: ''; display: block; width: 10px; height: 10px; position: absolute; top: -3px; background: ##000; transform: rotate(45deg); right: 5px; }
	.swBrowsePrograms .filter-btn { position: sticky; bottom: 10px; margin-right: 15px; margin-bottom: 8px; border: 0;text-shadow: none;float: right;border-radius: 2px; box-shadow: inset 0 2px 4px rgba(0,0,0,0.15),0 1px 2px rgba(0,0,0,0.05); color:##fff !important;}
	.swBrowsePrograms .swCatalogGrayBox { display: none; }
	.swBrowsePrograms .swCatalogTileBottomBox .attended { display: none; }
	.swBrowsePrograms .swRateExpand { position: relative; max-height: 100px; overflow: hidden;height:100px;}
	.swBrowsePrograms .swRateExpand::after { background: linear-gradient(rgba(255,255,255,0),rgba(255,255,255,.5),rgba(255,255,255,.9), rgb(255, 255, 255)); display: block; padding: 40px 0px; content: ''; position: absolute; bottom: 0; left: 0; width: 100%; }
	.swBrowsePrograms .swRateExpand > a { position: absolute; padding-left: 20px; z-index: 9; bottom: 0; }
	.swBrowsePrograms .swRateExpand > a::before { position: absolute; left: 0; width: 0; height: 0; content: '\f229'; top: 1px; margin: 0 auto; font-family:'bootstrap-icons'; font-size: 14px; font-weight: normal; }		
	.swBrowsePrograms .swRateExpand.open { max-height: initial !important; padding-bottom: 10px;height:auto !important; }
	.swBrowsePrograms .swRateExpand.open:after { display: none; }
	.swBrowsePrograms .swRateExpand.open > a::before { content: '\f235'; }
	.swBrowsePrograms .swBrowseProgramLeftContentArea button.ui-multiselect {width:100%!important;}
	.swBrowsePrograms .sortOptionsDropdown a.dropdown-toggle { background: ##f3f2f3; border-radius: 2px; border-color: ##e0e1e2; }
	.swBrowsePrograms .sortOptionsDropdown a.dropdown-toggle, .swBrowsePrograms .sortOptionsDropdown .dropdown-menu { min-width:260px; }
	.swBrowsePrograms .sortOptionsDropdown a.dropdown-toggle span, .swBrowsePrograms .sortOptionsDropdown .dropdown-menu li {font-size: 14px;}

	/*Pagination*/
	.swBrowsePrograms .pagination{text-align: center;}
	.swBrowsePrograms .pagination ul > li > a, .pagination ul > li > span{border: 1px solid ##e0e1e2;}
	@media only screen and (min-width:980px){
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a::after{border-bottom: 27px solid ##f3f2f3;}
		.swBrowsePrograms .swCatalogRightColumn{float: right !important;margin-left:auto;}
	}
	@media only screen and (min-width:768px){
		.swBrowsePrograms .swCatalogContentFrame.open > .span3{display: block;}
	}
	@media only screen and (max-width:1199px) {
		.swBrowsePrograms .prgmTitle { font-size: 1.7em!important; line-height: 1!important; margin: 5px 0px!important; }
		.swBrowsePrograms .prgmSubtitle { font-size: 16px!important; margin: 0px 0px 5px!important; }
		.swBrowsePrograms .prgmSubtitle small { font-size: 16px; }
		.swBrowsePrograms .swCatalogRightColumn { max-width: 200px; }
		.swBrowsePrograms .swCatalogProgramContent h2 { font-size: 18px; }
		.swBrowsePrograms .attended { font-size: 14px; }
		.swBrowsePrograms .attended small { margin-left: 10px; }
		.swBrowsePrograms .collapseSections .accordion-inner { padding: 15px; }
		.swBrowsePrograms .swCatalogCreditBox a.swCatalogCreditBoxCard h3 { font-size: 18px; }
		.swBrowsePrograms .swCatalogTileBox{min-width: 165px}
	}
	@media only screen and (max-width:991px) {
		.swBrowsePrograms .swCatalogToolBarFrame > div > ul > li { margin-right: 15px; }
		.swBrowsePrograms .sortOptionsDropdown a.dropdown-toggle, .swBrowsePrograms .sortOptionsDropdown .dropdown-menu { min-width: 190px; }
		.swBrowsePrograms .refine-btn { padding: 4px; font-size: 14px; }
		.swBrowsePrograms .swCatalogToolBarFrame ##swActiveFiltersCount, .swBrowsePrograms .swCatalogToolBarFrame .paginationInfo { font-size: 13px; }
		.swBrowsePrograms .swCatalogSideBarTop { padding: 15px 15px 15px 15px; }
		.swBrowsePrograms .collapseSections .accordion-toggle { padding: 8px 15px 8px 15px; }
		.swBrowsePrograms .collapseSections .accordion-inner { padding: 15px 15px; }
		.swBrowsePrograms .accordion-inner label { font-size: 14px; }
		.swBrowsePrograms .accordion-inner .checkbox span { margin-right: 10px; }
		.swBrowsePrograms .collapseSections .accordion-inner form { margin: 0; }
		.swBrowsePrograms .swCatalogContentFrame .swCatalogMiddleColumn { padding-right: 15px; }
		.swBrowsePrograms .swCatalogProgramContent h2 { font-size: 16px; }
		.swBrowsePrograms .swCatalogTileBottomBox { margin-top: 15px; }
		.swBrowsePrograms .swCatalogToolBar.second .muted.hidden{font-size: 0px;}
	}
	@media only screen and (max-width:979px){
		.swBrowsePrograms .swCatalogContentFrame.open > .span9{width: 100%;}
		.swBrowsePrograms .swCatalogContentFrame.open.xs-open > .span9{margin-left: 10px;}
		.swBrowsePrograms .swCatalogTileBox {min-width: 150px;}
		.swBrowsePrograms .swCatalogContentFrame.open .span3.swCatalogTileBox{min-width: 110px;}
		.swBrowsePrograms .swCatalogContentFrame .swCatalogPointBtn{text-align: left;}
	}
	 @media only screen and (max-width:767px) {
	 	.swBrowsePrograms .swCatalogToolBarFrame .swCatalogToolBarFrameFormatIcon {width:100%;}
	 	.swBrowsePrograms .swCatalogToolBarFrame .swCatalogToolBarFrameFormatIcon ul li a .bi{display: block;margin: 0;margin-bottom: 0px;text-align: center;font-size: 22px;margin-bottom: 5px;}
		.swBrowsePrograms .swCatalogToolBarFrame .nav-tabs{ justify-content: space-between; padding: 0px 0px; display: flex;}
		.swBrowsePrograms .swCatalogToolBar.second.flex { display: block; }
		.swBrowsePrograms .swCatalogToolBarFrame .span4.text-center { display: none; }
		.swBrowsePrograms .swCatalogToolBarFrame .last .sortOptionsDropdown { display: none; }
		.swBrowsePrograms .alpha-sort { margin: 0px 5px; }
		.swBrowsePrograms .alpha-sort i { width: 20px; text-align: center; }
		.swBrowsePrograms .refine-btn { padding: 5px 12px; font-size: 14px; margin-left: 5px; }
		.swBrowsePrograms .swCatalogToolBarFrame { align-items: center; margin: 0px 0px;display: flex; align-items: flex-start; }
		.swBrowsePrograms .swResults { margin-top: 20px; }
		.swBrowsePrograms .swWebinar, .swOnDemand, .mcEvent, .swBundle { font-size: 14px; display: inline-block; text-transform: uppercase !important; line-height: 24px; margin: 0px 5px; }
		.swBrowsePrograms .swWebinar i, .swBrowsePrograms .swOnDemand i, .swBrowsePrograms .mcEvent i, .swBrowsePrograms .swBundle i { font-size: 21px; margin-right: 10px; vertical-align: middle; }
		.swBrowsePrograms .swCatalogMiddleColumn .prgmCredits { display: none; }
		.swCatalogTileBottomBox span.offeredCred{display:none !important;}
		.swBrowsePrograms .swCatalogPointBtn { text-align: left; }
		.swBrowsePrograms .swCatalogTileBottomBox > ul > li:last-child { position: static; }
		.swBrowsePrograms .swCatalogTileBottomBox .btnBox { display: flex; justify-content: end; align-items: center; float: right; }
		.swBrowsePrograms .swCatalogTileBottomBox .btnBox .addcart { padding: 5px 25px; margin: 5px 0px 5px 15px; display: inline-block; }
		.swBrowsePrograms .swCatalogPointBtn .attended { position: absolute; top: 0; right: 0; display: block; }
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn.open a::after { display: none; }
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn a { margin-bottom: 10px; }
		.swBrowsePrograms .swCatalogPointBtn .swCatalogPoints { margin-bottom: 10px; }
		.swBrowsePrograms .swCatalogGrayBox { padding: 20px 25px; background: ##f3f2f3; margin-bottom: 10px; display: none !important; }
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a {display:inline-block !important;}
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a::after{border-bottom: 27px solid ##f3f2f3;}
		.swBrowsePrograms .swCatalogGrayBox h5 { margin: 0; text-transform: uppercase !important; font-weight: 500; margin-bottom: 5px; }
		.swBrowsePrograms .swCatalogGrayBox > div { margin-bottom: 15px; }
		.swBrowsePrograms .swCatalogGrayBox ul li.swLearningObjPoint::before { position: absolute; top: 5px; left: 0px; display: block; content: '\f26e'; font-family:'bootstrap-icons'; font-weight: normal; }
		.swBrowsePrograms .swCatalogGrayBox ul li.swIncProgramPoint::before { position: absolute; top: 5px; left: 0px; display: block; content: '\f26e'; font-family:'bootstrap-icons'; font-weight: normal; }
		.swBrowsePrograms .swCatalogGrayBox ul li.swSpeakersPoint::before { position: absolute; top: 5px; left: 0px; display: block; content: '\1F464'; font-family:'bootstrap-icons'; font-weight: normal; }
		.swBrowsePrograms .swCatalogGrayBox li { position: relative; padding-left: 22px; font-size: 14px; }
		.swBrowsePrograms .swCatalogPointBtn.open .swCatalogPoints { display: none; }
		.swBrowsePrograms .swCatalogPointBtn.open1 .swCatalogPoints { display: none; }
		.swBrowsePrograms .swCatalogPointBtn.open .swCatalogGrayBox { display: block !important; }
		.swBrowsePrograms .swCatalogPointBtn.open1 .swCatalogGrayBox { display: block !important; }
		.swBrowsePrograms .swCatalogGrayBox > div small { float: right; }
		.swBrowsePrograms .swCatalogRightColumn { display: none; }
		.swBrowsePrograms .filter-btn { position: static; bottom: 0px; }
		.swBrowsePrograms .swCatalogToolBarFrame > div > ul > li { margin-right: 0; }
		.swBrowsePrograms .swCatalogToolBarFrame ul li a { font-weight: 500; font-size: 12px; text-align: center; line-height: 18px; line-height: 18px; text-transform: uppercase !important; margin: 0; padding: 0px;}
		.swBrowsePrograms .swCatalogToolBarFrame > .span4:last-child { width: auto; }
		.swBrowsePrograms .alpha-sort ul li a { text-align: left; padding: 0; }
		.swBrowsePrograms .swCatalogContentFrame > .span9 { width: 100%; margin-left: 0; }
		.swBrowsePrograms .swCatalogTitleTopBox { display: block !important; }
		.swBrowsePrograms .swCatalogIconBox > ul > li > a > i { font-size: 18px;margin-bottom: 5px; }
		.swBrowsePrograms .swCatalogIconBox > ul > li { padding: 0px 5px 0px 0px; }
		.swBrowsePrograms .swCatalogTileBox { width: 100px !important; min-width: 100px; float: left !important; margin-right: 10px; }
		.swBrowsePrograms .swCatalogMiddleColumn span.featuredLabel { font-size: 10px;float: left; margin-right: 5px; margin-bottom: 0px;}
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a { font-size: 0; }
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a::before { top: 2px;font-size: 14px; }
		.swBrowsePrograms .swCatalogTileBottomBox li.swCatalogPointBtn > a span { font-size: 14px; }
		.swBrowsePrograms .swCatalogContentFrame .swCatalogMiddleColumn { min-height: 100px; padding-right: 0px;}
		.swBrowsePrograms .swCatalogContentFrame{flex-wrap: wrap;}
		.swBrowsePrograms .swCatalogContentFrame.open > .span9 { width: 100%; margin-left: 0; display: none; }
		.swBrowsePrograms .swCatalogContentFrame.open > .span3{display: block;}
		.swBrowsePrograms .swCatalogContentFrame.open > .span3,
		.swBrowsePrograms .swCatalogContentFrame.xs-open > .span3,
		.swBrowsePrograms .swCatalogContentFrame.open > .span9 { width: 100%; }
		.swBrowsePrograms .swCatalogTiles .swCatalogTileResult { padding: 15px; margin: 0px -15px; }
		.swBrowsePrograms .swCatalogLoadMore { font-weight: 600; text-align: center; margin: 15px 0px; }
		.swBrowsePrograms .swCatalogContentFrame > .span3{border-right: 0px; border-left: 0px;}
		.swBrowsePrograms .swCatalogToolBarFrame .last .dropdown-menu i{margin-right: 0px;}
		.swBrowsePrograms .swCatalogToolBarFrame .last .visible-phone.alpha-sort i{margin-right: 0px;}
		.swBrowsePrograms .swCatalogToolBarFrame .last .visible-phone.alpha-sort .dropdown-menu i{margin-right: 15px;}
		.swBrowsePrograms .swCatalogToolBarFrame .last{margin-top: 5px;}
		.swBrowsePrograms .attended i{font-size: 12px;}
		.swBrowsePrograms .swCatalogMiddleColumn a.visible-phone{font-size: 13px;line-height: 1.4}
		.swBrowsePrograms .swCatalogMiddleColumn a.visible-phone i{font-size: 12px;margin-right: 5px;}
		.swBrowsePrograms .prgmSubtitle small{display: inline;}
		.swBrowsePrograms .pagination{display: none;}
		.ui-multiselect-menu.swBrowseFilter{ min-width:65% !important; }
	}
	@media only screen and (max-width:359px) {
	 	.swBrowsePrograms .swCatalogMiddleColumn a.visible-phone{font-size: 10px;}
	}
	<!--- Browse Programs Page CSS : End --->
	</style>

	<script>
	function viewSWSavedPrograms() {
		var savedPrograms = $('i.sw_savedprogramscounticon').attr('data-swsavedprogramscount');
		if (savedPrograms > 0)
			self.location.href = '#attributes.event.getValue('mainurl')#&panel=browse&_swis=1';
		else
			return false;
	}
	function saveSWProgramForLater(pid,ft,mode) {
		var saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				var saveIcon = $('i.sw_savedprogramscounticon');
				var currentCount = Number(saveIcon.attr('data-swsavedprogramscount')) + 1;
				if (saveIcon.hasClass('muted')) {
					saveIcon.addClass('swIconBadge swRed').removeClass('muted');
					toggleSavedProgramsIconLink(true);
				}
				saveIcon.attr('data-swsavedprogramscount', currentCount);

				<!--- mobile menu --->
				var menuItemMobile = $('a.savedProgramsMobileMenuItem');
				if (menuItemMobile.hasClass('muted')) menuItemMobile.removeClass('muted');
				menuItemMobile.find('span.savedCount').text('('+ (currentCount) +')');
			}
		};
		var objParams = { programID:pid, programType:ft };
		TS_AJX('SWBROWSE','saveSWProgramForLater',objParams,saveResult,saveResult,10000,saveResult);
	}
	function removeSavedSWProgram(pid,ft,mode) {
		var removeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				var saveIcon = $('i.sw_savedprogramscounticon');
				var currentCount = Number(saveIcon.attr('data-swsavedprogramscount')) - 1;
				saveIcon.attr('data-swsavedprogramscount', currentCount);
				if (currentCount == 0) {
					saveIcon.removeClass('swIconBadge swRed').addClass('muted');
					toggleSavedProgramsIconLink(false);
				}

				<!--- mobile menu --->
				var menuItemMobile = $('a.savedProgramsMobileMenuItem');
				menuItemMobile.find('span.savedCount').text(currentCount > 0 ? '('+ (currentCount) +')' : '');
				if (currentCount == 0) menuItemMobile.addClass('muted');
				
			}
		};
		var objParams = { programID:pid, programType:ft };
		TS_AJX('SWBROWSE','removeSavedSWProgram',objParams,removeResult,removeResult,10000,removeResult);
	}
	function toggleSavedProgramsIconLink(f){
		if(f){
			$(".swSavedProgramsIconLink").attr('title', 'View saved').tooltip('fixTitle');
			$(".swSavedProgramsIconLink").removeClass('muted');
		}
		else {
			$(".swSavedProgramsIconLink").removeAttr('data-original-title');
			$(".swSavedProgramsIconLink").addClass('muted');
		}
	}
	function returnToCLECatalog(){
		var #ToScript("#attributes.event.getValue('mainurl')#&panel=browse","swCatalogBrowseUrl")#
		<cfset local.swCatalogBrowseFilters = application.mcCacheManager.sessionGetValue(keyname='swCatalogBrowseFilters', defaultValue={})>
		<cfif local.swCatalogBrowseFilters.count()>
			swCatalogBrowseUrl += '&#local.swCatalogBrowseFilters.URLQueryString#';
			<cfif local.swCatalogBrowseFilters.startPos gt 0>
				swCatalogBrowseUrl += '&_sw_sp=#local.swCatalogBrowseFilters.startPos#';
			</cfif>
		</cfif>
		location.href = swCatalogBrowseUrl;
	}
	function sw_showPrivacyStatement() {
		var swPrivacyStatement = '<div style="margin:10px;"><b>PRIVACY STATEMENT</b><br/><br/>SeminarWeb collects personally identifiable information when you visit our site. We also automatically receive and record information on our server logs from your browser including your IP address, cookie information and the page(s) you have visited for internal purposes. When you register for a program, your personally identifiable information, including your contact information and time you spent participating in this online program, may be shared with the organization(s) presenting this program for purposes of CE accreditation, tracking and marketing.  Your personal credit card data is tokenized, and therefore we never store your personal credit card data, nor do we share your credit card data with the organization sponsoring this program; we are fully compliant with Payment Card Industry Data Standards.</div>';
		$.colorbox( {innerWidth:500, innerHeight:260, html:swPrivacyStatement, overlayClose:false} );
	}

	$(function() {
		var tooltipConfig = { container: 'body' }
		$('.tooltip-icon').tooltip(tooltipConfig);

		$(".swCatalog .swHeartIcon i.sw_saveforlater").on('click', function() {
			var saveMode = $(this).data('swsaveforlatermode');

			if ($(this).hasClass('swRed'))
				removeSavedSWProgram($(this).data('swprogramid'),$(this).data('swprogramtype'),saveMode);
			else
				saveSWProgramForLater($(this).data('swprogramid'),$(this).data('swprogramtype'),saveMode);
			
			switch(saveMode) {
				case 'browse':
					$(this).toggleClass("swRed");
					break;
				case 'detail':
					$(this).toggleClass("swRed bi-heart bi-heart-fill");
					break;
			};
		});
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.swCatalogCommonCSS#">