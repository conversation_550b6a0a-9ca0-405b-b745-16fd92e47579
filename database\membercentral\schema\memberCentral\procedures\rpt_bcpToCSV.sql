ALTER PROC dbo.rpt_bcpToCSV
@reportID int,
@filepath varchar(400),
@tblName varchar(100)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @csvfilename varchar(400) = @filepath + '.csv';

	-- ensure files exist
	if dbo.fn_fileExists(@filepath + '.sql') = 0 or dbo.fn_fileExists(@filepath + '.bcp') = 0
		RAISERROR('Data files no longer exist.',16,1);

	-- read in sql create script and create global temp table
	declare @execcmd varchar(max);
	select @execcmd = dbo.fn_ReadFile(@filepath + '.sql',0,0);
	if len(@execcmd) = 0
		RAISERROR('Data definition file is invalid.',16,1);
	EXEC(@execcmd);

	-- bcp in data
	declare @bcpcmd varchar(8000) = 'bcp ' + @tblName + ' in ' + @filepath + '.bcp -n -T -S ' + CAST(serverproperty('servername') as varchar(40));
	exec master..xp_cmdshell @bcpcmd, NO_OUTPUT;

	-- create select clause
	-- use number table to guarantee order of nodes. then loop to guarantee order.
	declare @otherXML xml;
	declare @tblColumns TABLE (autoID int, colName varchar(400), realColName varchar(400));

	select @otherXML = otherXML from dbo.rpt_savedReports where reportID = @reportID;
	insert into @tblColumns (autoID, colName, realColName)
	select N.Number,
		case 
		when T.c.value('(@field)[1]','varchar(400)') = T.c.value('(@label)[1]','varchar(400)') then quotename(T.c.value('(@field)[1]','varchar(400)'))
		else quotename(T.c.value('(@field)[1]','varchar(400)')) + ' as ' + quotename(T.c.value('(@label)[1]','varchar(400)')) 
		end,
		quotename(T.c.value('(@field)[1]','varchar(400)'))
	from dbo.F_TABLE_NUMBER_RANGE(1,500) as N
	cross apply @otherXML.nodes('/report/customcsv/fldorder/fld[sql:column("N.Number")]') as T(c)
	where N.Number between 1 and @otherXML.value('count(/report/customcsv/fldorder/fld)','int');

	-- use all temp table cols if no customcsv fields defined
	IF @@ROWCOUNT = 0
		INSERT INTO @tblColumns (autoID, colName, realColName)
		SELECT column_id, QUOTENAME([name]), QUOTENAME([name])
		FROM tempdb.sys.columns 
		WHERE object_id = object_id('tempdb..'+@tblName);

	declare @selectClause varchar(max) = '', @fieldID int, @firstColumn varchar(400) = '', @colName varchar(400);
	select @fieldID = min(autoID) from @tblColumns;
	while @fieldID is not null begin
		select @colName = colName from @tblColumns where autoID = @fieldID;
		set @selectClause = @selectClause + ',' + @colName;
		if @firstColumn = ''
			set @firstColumn = @colName;
		select @fieldID = min(autoID) from @tblColumns where autoID > @fieldID;
	end
	set @selectClause = substring(@selectClause,2,len(@selectClause));

	-- create order by clause
	-- use number table to guarantee order of nodes. then loop to guarantee order.
	declare @tblSortColumns TABLE (autoID int, colName varchar(400), dir char(4));
	insert into @tblSortColumns (autoID, colName, dir)
	select N.Number, quotename(T.c.value('(@field)[1]','varchar(400)')), T.c.value('(@dir)[1]','char(4)')
	from dbo.F_TABLE_NUMBER_RANGE(1,100) as N
	cross apply @otherXML.nodes('/report/customcsv/fldsort/fld[sql:column("N.Number")]') as T(c)
	where N.Number between 1 and @otherXML.value('count(/report/customcsv/fldsort/fld)','int');

	declare @orderByClause varchar(max) = '';
	set @fieldID = null;
	select @fieldID = min(autoID) from @tblSortColumns;
	while @fieldID is not null begin
		set @orderByClause = @orderByClause + ',' + (select colName + ' ' + dir from @tblSortColumns where autoID = @fieldID);
		select @fieldID = min(autoID) from @tblSortColumns where autoID > @fieldID;
	end
	set @orderByClause = substring(@orderByClause,2,len(@orderByClause));

	-- if no order by, choose the first column in the select list
	IF len(@orderByClause) = 0
		set @orderByClause = @firstColumn;
		
	-- create statement for csv export
	DECLARE @selectsql varchar(max) = '
		SELECT ' + @selectClause + ', ROW_NUMBER() OVER(order by ' + @orderByClause + ') as mcCSVorder 
		*FROM* ' + @tblName;
	EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;
	
	-- return number or rows in the csv
	EXEC('SELECT COUNT(*) as csvRowCount FROM ' + @tblName);
	
	EXEC('DROP TABLE ' + @tblName);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
