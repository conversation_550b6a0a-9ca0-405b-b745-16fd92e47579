<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">
	<cfset this.siteResourceID = 0>
	<cfset variables.applicationReservedURLParams = "mca_s,mca_tt,mca_a,mca_ta,mca_hk,mca_lt,mca_ajaxlib,mca_jsonfun,jumpToTool">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.viewToUse = "echo">

		<cfscript>
			local.strApp.data = "";
			
			// set up params ----------------------------------------------------------------------------------------------
			arguments.event.getCollection()['mc_adminNav']['adminHome'] = '/?pg=admin';
			arguments.event.getCollection()['mc_adminNav']['adminHomeResource'] = '/?event=cms.showResource&resID=#this.siteResourceID#';
			arguments.event.getCollection()['mc_adminNav']['breadCrumbs'] = arrayNew(1);
			arguments.event.getCollection()['mc_adminNav']['currentNavigationItem'] = structNew();

			//is this an ajax request
			local.mca_ajaxlib = arguments.event.getValue('mca_ajaxlib','');
			local.mca_jsonlib = arguments.event.getValue('mca_jsonlib','');

			local.objNavBuilder	= createObject("component","model.admin.navBuilder");
			local.appInstanceSettings = getInstanceSettings(this.appInstanceID);
			arguments.event.getCollection()['mc_adminAppInfo']['appInstanceSettings'] = local.appInstanceSettings;
			arguments.event.getCollection()['mc_adminAppInfo']['siteResourceID'] = this.siteResourceID;

			if (len(local.mca_ajaxlib)) {
				local.strApp = runAjaxRequest(arguments.event,local.mca_ajaxlib);
				local.viewToUse = local.strApp.view; 
			} else if (len(local.mca_jsonlib)) {
				local.strApp = runJSONRequest(arguments.event,local.mca_jsonlib);
				local.viewToUse = local.strApp.view; 
			} else {
				// create navigation object for all modes but stream and direct 
				if (arguments.event.valueExists('jumpToTool') OR NOT listFindNoCase('stream,direct',arguments.event.getValue("mc_pageDefinition.layoutmode"))) {

					//trigger refresh of nav if applicationXML is newer than session
					if (not isDefined("session.mcastruct.strNavKeysTimeStamp") or ( StructKeyExists(application,"adminNavigationXMLTimeStamp") and (session.mcastruct.strNavKeysTimeStamp lt application.adminNavigationXMLTimeStamp))) {
						// wait 5 mins before triggering refresh for user to limit multiple refreshed due to rolling restarts of containers
						if ((GetTickCount() - application.adminNavigationXMLTimeStamp) gte 300000) {
							arguments.event.setValue("refreshAdminNavData",1);
						}
					}
				
					if (arguments.event.valueExists("refreshAdminNavData") or not isDefined("session.mcastruct.strNavKeys")) {
						session.mcastruct.strNavKeysTimeStamp =  GetTickCount();
						session.mcastruct.strNavKeys = local.objNavBuilder.getUserNavKeys(memberID=session.cfcuser.memberData.memberid, mc_siteinfo=arguments.event.getValue('mc_siteinfo'), settingsXML=local.appInstanceSettings.settingsXML);
					}
				}

				if (arguments.event.valueExists('jumpToTool')) {
					local.redQS = "";
					// reserved application params and pg should not be appended to redirect url
					local.redQSExclusionList = listappend(variables.applicationReservedURLParams,"pg");
					for (local.x in url) {
						if (not listfindnocase(local.redQSExclusionList,local.x)) 
							local.redQS = local.redQS & "&#lcase(local.x)#=#url[local.x]#";
					}

					// handle redirects
					local.jtt = arguments.event.getTrimValue('jumpToTool');
					switch (local.jtt) {
						case "EmailBlast|searchEmailActivity|showEmailActivity":
							local.jtt = "MemberSettingsAdmin|searchEmailActivity|showEmailActivity";
							break;
					}

					// set elements 2 and 3 to '' if they do not exist
					local.arrJTT = listToArray(local.jtt,'|');
					if (arrayLen(local.arrJTT) is 1) local.arrJTT[2] = '';
					if (arrayLen(local.arrJTT) is 2) local.arrJTT[3] = '';

					local.gotoURL = buildLinkToTool(toolType=local.arrJTT[1], mca_ta=local.arrJTT[3], navMethod=local.arrJTT[2]);
					application.objCommon.redirect(urlToGoTo="#local.gotoURL##local.redQS#");
				} else {

					// dont do this in stream or direct
					if (NOT listFindNoCase('stream,direct',arguments.event.getValue("mc_pageDefinition.layoutmode"))) {
						CreateObject("component","model.support.support").zenDeskWebWidgetJWT();

						if (isDefined('session.mcastruct.strNavKeys') and NOT structIsEmpty(session.mcastruct.strNavKeys)) {
							local.objNavBuilder.addMissingNavLevelsToLink(event=arguments.event, strNavKeys=session.mcastruct.strNavKeys);
							arguments.event.getCollection()['mc_adminNav']['currentNavigationItem'] = local.objNavBuilder.populateCurrentNavStruct(mca_a=arguments.event.getValue('mca_a'));
							arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = (application.objUser.isSuperUser(cfcuser=session.cfcuser) ? ucase(arguments.event.getValue('mc_siteinfo.siteCode')) & ": " : '') & arguments.event.getCollection()['mc_adminNav']['currentNavigationItem']['navName'];
						}
					}

					// handle redirects for tools that have moved
					if (isDefined('session.mcastruct.strNavKeys') and NOT structIsEmpty(session.mcastruct.strNavKeys)) {
						local.objNavBuilder.redirectTools(mca_s=arguments.event.getValue('mca_s',''), mca_a=arguments.event.getValue('mca_a',''), mca_tt=arguments.event.getValue('mca_tt',''), mca_ta=arguments.event.getValue('mca_ta',''));
					}

					if (int(val(arguments.event.getValue('mca_hk',0))) gt 0) {
						// return list of children for this nav item ------------------------------------------------------------------
						local.strApp.data = buildNav4(event);
					} else if (int(val(arguments.event.getValue('mca_tt',0))) gt 0) {
						// run application and return data ----------------------------------------------------------------------------
						local.toolType = getToolByToolTypeID(arguments.event.getValue('mca_tt'));
						if (local.toolType.recordCount) {
							arguments.event.getCollection()['mc_admintoolInfo']['toolType'] = local.toolType;
							local.strApp = CreateObject("component","model.admin.#local.toolType.toolCFC#").runDefaultEvent(arguments.event,this.appInstanceID,local.toolType.siteResourceID);
						} else {
							application.objCommon.redirect('/?pg=admin');
						}
					} else {
						local.strApp.data = 'Access denied.';
					}

				}
			}
		</cfscript>

		<cfreturn returnAppStruct(local.strApp.data,local.viewToUse)>	
	</cffunction>
	
	<cffunction name="getToolName" returntype="string" access="public" output="false" cachedwithin="#CreateTimeSpan(0,0,30,0)#">
		<cfargument name="navigationID" type="Numeric" required="TRUE">
		<cfset var qryTool = "">
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTool">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT navName
			FROM dbo.admin_navigation
			WHERE navigationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.navigationID#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>		
		<cfreturn qryTool.navName>
	</cffunction>
	
	<cffunction name="getToolByToolTypeID" access="public" returntype="query" output="false">
		<cfargument name="toolTypeID" type="numeric" required="TRUE">
		<cfset var qryTool = "">
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTool" cachedwithin="#CreateTimeSpan(0,0,10,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


			SELECT att.toolTypeID, att.resourceTypeID, att.toolType, att.toolCFC, sr.siteResourceID
			FROM dbo.admin_toolTypes att
				inner join dbo.cms_siteResources sr
					on att.resourceTypeID = sr.resourceTypeID
					and sr.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID#">
					and att.toolTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.toolTypeID#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>	
		<cfreturn qryTool>
	</cffunction>
	
	<cffunction name="getToolIDByName" access="public" returntype="numeric" output="false">
		<cfargument name="toolType" type="string" required="TRUE">
		<cfset var qryTool = "">
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTool" cachedwithin="#CreateTimeSpan(0,0,30,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT toolTypeID
			FROM dbo.admin_toolTYpes
			WHERE toolType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.toolType#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>	
		<cfreturn qryTool.toolTypeID>
	</cffunction>

	<cffunction name="buildCurrentLink" access="private" returntype="string" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="mca_ta" type="string" required="TRUE">
		<cfargument name="useShowResourceBase" type="boolean" required="false" default="false">
		
		<cfscript>
			var local = structNew();
			local.appInstanceID = arguments.event.getValue('appID',0);
			if (arguments.useShowResourceBase) local.newURL = arguments.event.getValue('mc_adminNav.adminHomeResource');
			else local.newURL = arguments.event.getValue('mc_adminNav.adminHome');
			
			if (isnumeric(local.appInstanceID) and (local.appInstanceID gt 0)) local.newURL = local.newURL & '&appID=' & local.appInstanceID;
			
			local.newURL = local.newURL & '&mca_s=' & arguments.event.getValue('mca_s');
			local.newURL = local.newURL & '&mca_a=' & arguments.event.getValue('mca_a');
			local.newURL = local.newURL & '&mca_tt=' & arguments.event.getValue('mc_admintoolInfo.toolType.tooltypeID');
			local.newURL = local.newURL & '&mca_ta=' & arguments.mca_ta;
			if (arguments.event.valueExists('mca_lt'))
				local.newURL = local.newURL & '&mca_lt=' & arguments.event.getValue('mca_lt');
		</cfscript>
		
		<cfreturn local.newURL>
	</cffunction>
		
	<cffunction name="buildLinkToTool" access="public" returntype="string" output="false">
		<cfargument name="toolType" type="string" required="true">
		<cfargument name="mca_ta" type="string" required="false" default="">
		<cfargument name="navMethod" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.mca_s = ""> 
		<cfset local.mca_a = "">
		<cfset local.mca_tt = "">
		<cfset local.mca_ta = "">
		<cfset local.mca_lt = "">
		
		<cftry>
			<cfif len(arguments.navMethod)>
				<cfset local.linkedToolXML = XMLSearch(application.adminNavigationXML,"//navitem[@toolType='#arguments.toolType#' and @cfcMethod='#arguments.navMethod#' and (@navAreaID='3' or @navAreaID='4')]")>
			<cfelse>
				<cfset local.linkedToolXML = XMLSearch(application.adminNavigationXML,"//navitem[@toolType='#arguments.toolType#' and (@navAreaID='3' or @navAreaID='4')]")>
			</cfif>

			<cfif arrayLen(local.linkedToolXML)>
				<cfif local.linkedToolXML[1].xmlattributes.navAreaID eq 3>
					<cfset local.mca_s = XMLSearch(local.linkedToolXML[1],"string(../../@navigationID)")>
				<cfelse>
					<cfset local.mca_s = XMLSearch(local.linkedToolXML[1],"string(../../../@navigationID)")>
					<cfset local.mca_lt = XMLSearch(local.linkedToolXML[1],"string(../@navigationID)")>
				</cfif>
				<cfset local.mca_a = local.linkedToolXML[1].xmlattributes.navigationID>
				<cfset local.mca_tt = local.linkedToolXML[1].xmlattributes.toolTypeID>
				<cfif len(arguments.mca_ta)>
					<cfset local.mca_ta = arguments.mca_ta>
				<cfelse>
					<cfset local.mca_ta = local.linkedToolXML[1].xmlattributes.cfcMethod>
				</cfif>
			</cfif>

			<cfif len(local.mca_tt)>
				<cfif len(local.mca_lt)>
					<cfset local.returnLink = "/?pg=admin&mca_s=#local.mca_s#&mca_a=#local.mca_a#&mca_tt=#local.mca_tt#&mca_ta=#local.mca_ta#&mca_lt=#local.mca_lt#">
				<cfelse>
					<cfset local.returnLink = "/?pg=admin&mca_s=#local.mca_s#&mca_a=#local.mca_a#&mca_tt=#local.mca_tt#&mca_ta=#local.mca_ta#">
				</cfif>
			<cfelse>
				<cfset local.returnLink = "">
			</cfif>
		<cfcatch type="any">
			<cfset local.returnLink = "">
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnLink>
	</cffunction>

	<cffunction name="appendBreadCrumbs" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="crumb" type="struct" required="TRUE">
		<cfif NOT listFindNoCase('stream,direct',arguments.event.getValue("mc_pageDefinition.layoutmode"))>
			<cfset arguments.event.getCollection()['mc_adminNav']['currentNavigationItem']['arrCrumbs'].append(arguments.crumb)>
			<cfset arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = (application.objUser.isSuperUser(cfcuser=session.cfcuser) ? ucase(arguments.event.getValue('mc_siteinfo.siteCode')) & ": " : '') & arguments.crumb.text>
		</cfif>
	</cffunction>

	<cffunction name="checkRights" access="private" returntype="boolean" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="functionName" type="string" required="TRUE">
		
		<cfset var local = structNew()>
		<cfset local.allowed = false>
		<cfset local.strRights = arguments.event.getValue('mc_admintoolInfo.myRights')>
		
		<cfif structKeyExists(local.strRights,arguments.functionName) AND local.strRights[arguments.functionName] is 1>
			<cfset local.allowed = true>				
		</cfif>
		
		<cfreturn local.allowed>
	</cffunction>

	<cffunction name="repostForm" access="public" output="true" returntype="struct" hint="repostForm">
		<cfargument name="Event" type="any">
		<cfargument name="form" type="Struct">
		<cfscript>
			var local = structNew();
			local.postURL = "";			
			local.postURL = arguments.event.getValue('repostURL');
			if( arguments.event.valueExists('msg') ){
				local.postURL = local.postURL & "&msg=" & arguments.event.getValue('msg');
			}
		</cfscript>		
		<cfsavecontent variable="local.data">
			<cfoutput>	
				<form action="#local.postURL#" name="frmRepost" method="POST">
					<cfloop list="#arguments.form.fieldNames#" index="local.fName">
						<input type="hidden" name="#local.fName#" value="#evaluate('form.' & local.fName)#"><br />
					</cfloop>
				</form>
				<script>
					document.frmRepost.submit();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="runAjaxRequest" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="ajaxlib" type="string" required="TRUE">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { data='', view='xml' }>
		
		<cfswitch expression="#arguments.ajaxlib#">		
			<cfcase value="sponsors">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.sponsors.sponsors")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editSponsor">
						<cfset local.returnString = local.libCFC.editSponsor(siteID=arguments.event.getValue('mc_siteinfo.siteID'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), sponsorID=arguments.event.getValue('sponsorID',0), referenceType=arguments.event.getValue('_srt'), referenceID=arguments.event.getValue('_srid'), widgetSelectorID=arguments.event.getValue('widgetSelectorID'), editSelected=arguments.event.getValue('editSelected',false))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editSponsorGroupForm">
						<cfset local.returnString = local.libCFC.editSponsorGroupForm(siteID=arguments.event.getValue('mc_siteinfo.siteID'), sponsorGroupingID=arguments.event.getValue('sponsorGroupingID',0), referenceType=arguments.event.getValue('_srt'), referenceID=arguments.event.getValue('_srid'), widgetSelectorID=arguments.event.getValue('widgetSelectorID'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="showAssociateSponsorForm">
						<cfset local.returnString = local.libCFC.showAssociateSponsorForm(siteID=arguments.event.getValue('mc_siteinfo.siteID'), sponsorID=arguments.event.getValue('sponsorID',0), referenceType=arguments.event.getValue('_srt'), referenceID=arguments.event.getValue('_srid'), widgetSelectorID=arguments.event.getValue('widgetSelectorID'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="resourceCustomFields">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.customFields.customFields")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addField">
						<cfset local.returnString = local.libCFC.addField(siteID=arguments.event.getValue('mc_siteinfo.siteID'), orgid=arguments.event.getValue('mc_siteinfo.orgid'), csrid=arguments.event.getValue('_cfcsrid'), usageRT=arguments.event.getValue('_cfur'), usageAN=arguments.event.getValue('_cfua'), detailID=arguments.event.getValue('_cfdtid',0), gridExt=arguments.event.getValue('_cfg'), event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editField">
						<cfset local.returnString = local.libCFC.editField(siteID=arguments.event.getValue('mc_siteinfo.siteID'), orgid=arguments.event.getValue('mc_siteinfo.orgid'), fieldID=arguments.event.getValue('fieldID'), csrid=arguments.event.getValue('_cfcsrid'), usageID=arguments.event.getValue('_cfuid'), detailID=arguments.event.getValue('_cfdtid',0), gridExt=arguments.event.getValue('_cfg'), event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editFieldValue">
						<cfset local.returnString = local.libCFC.editFieldValue(fieldID=arguments.event.getValue('fieldID'), valueID=arguments.event.getValue('valueID'), csrid=arguments.event.getValue('_cfcsrid'), usageID=arguments.event.getValue('_cfuid'), gridExt=arguments.event.getValue('_cfg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editFieldGrouping">
						<cfset local.returnString = local.libCFC.editFieldGrouping(siteID=arguments.event.getValue('mc_siteinfo.siteID'), orgid=arguments.event.getValue('mc_siteinfo.orgid'), fieldGroupingID=arguments.event.getValue('fgID'), csrid=arguments.event.getValue('_cfcsrid'), usageID=arguments.event.getValue('_cfuid'), detailID=arguments.event.getValue('_cfdtid',0), gridExt=arguments.event.getValue('_cfg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="renderFeaturedImageField">
						<cfset local.returnString = local.libCFC.renderFeaturedImageField(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), fieldID=arguments.event.getValue('fieldID'), itemID=arguments.event.getValue('_cfitemid'), itemType=arguments.event.getValue('_cfitemtype'), saveImageHandler=arguments.event.getValue('_cfsaveimghandler',''), deleteImageHandler=arguments.event.getValue('_cfdeleteimghandler',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="resourceNotes">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.resourceNotes.resourceNotes")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addResourceNote">
						<cfset local.returnString = local.libCFC.addResourceNote(siteID=arguments.event.getValue('mc_siteinfo.siteID'), viewMode=arguments.event.getValue('rn_vw'), resourceType=arguments.event.getValue('rn_rt'), csrid=arguments.event.getValue('rn_csrid'), memberID=arguments.event.getValue('rn_mid'), categoryID=arguments.event.getValue('rn_cid'), usageType=arguments.event.getValue('rn_ut'), itemID=arguments.event.getValue('rn_id'), showDescOnly=arguments.event.getValue('rn_descOnly'), retFunction=arguments.event.getValue('rn_ret'), cancelFunction=arguments.event.getValue('rn_cancel'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>		
			<cfcase value="dhtmlGrid">
				<cfswitch expression="#arguments.event.getValue('com','')#">	
					<!--- categoryXML.cfc --->
					<cfcase value="categoryXML">
						<cfset local.objCom 			= createObject("component","model.admin.categories.categoryXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- virtualGroupsXML.cfc --->
					<cfcase value="virtualGroupsXML">
						<cfset local.objCom 			= createObject("component","model.admin.virtualGroups.virtualGroupsXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- fileShareXML.cfc --->
					<cfcase value="fileShareXML">
						<cfset local.objCom 			= createObject("component","model.admin.fileShare.fileShareXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- memberXML.cfc --->
					<cfcase value="memberXML">
						<cfset local.objCom 			= createObject("component","model.admin.members.memberXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- subscriptionXML.cfc --->
					<cfcase value="subscriptionXML">
						<cfset local.objCom 			= createObject("component","model.admin.subscriptions.subscriptionXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- referralsXML.cfc --->
					<cfcase value="referralsXML">
						<cfset local.objCom = createObject("component","model.admin.referrals.referralsXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<!--- adsXML.cfc --->
					<cfcase value="adsXML">
						<cfset local.objCom = createObject("component","model.admin.ads.adsXML")>
						<cfset local.returnXML 		= local.objCom.controller(arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="badgeTemplates">
				<cfset local.libCFC = createObject("component","model.admin.badges.deviceAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="getBadgeTemplatesXML">
						<cfset local.returnXML = local.libCFC.getBadgeTemplatesXML(siteID=arguments.event.getValue('mc_siteinfo.siteID'), treeCode=arguments.event.getValue('_bttreecode'), gridExt=arguments.event.getValue('_btg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="editBadgeTemplate">
						<cfset local.returnString = local.libCFC.editBadgeTemplate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), templateID=arguments.event.getValue('templateID',0), treeCode=arguments.event.getValue('_bttreecode'), gridExt=arguments.event.getValue('_btg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editBadgeTemplateCategory">
						<cfset local.returnString = local.libCFC.editBadgeTemplateCategory(categoryID=arguments.event.getValue('categoryID',0), treeCode=arguments.event.getValue('_bttreecode'), gridExt=arguments.event.getValue('_btg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="printPreview">
						<cfset local.returnString = local.libCFC.printPreview(siteID=arguments.event.getValue('mc_siteinfo.siteID'), templateID=arguments.event.getValue('templateID',0), treeCode=arguments.event.getValue('_bttreecode'), gridExt=arguments.event.getValue('_btg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="emailBlast">
				<cfset local.libCFC = createObject("component","model.admin.emailBlast.EmailBlastAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="resendEmail">
						<cfset local.returnString = local.libCFC.resendEmail(siteID=arguments.event.getValue('mc_siteinfo.siteID'), recipientID=arguments.event.getValue('rID',0), toEmail=arguments.event.getValue('toEmail',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="doResendEmail">
						<cfset local.returnString = local.libCFC.doResendEmail(siteID=arguments.event.getValue('mc_siteinfo.siteID'), recipientID=arguments.event.getValue('rID',0), toEmail=arguments.event.getValue('toEmail',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="downloadMessageAttachment">
						<cfset local.returnString = local.libCFC.downloadMessageAttachment(recipientID=arguments.event.getValue('rID',0), attachmentID=arguments.event.getValue('aID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="emailTemplates">
				<cfset local.libCFC = createObject("component","model.admin.emailTemplates.emailTemplateAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="getEmailTemplatesXML">
						<cfset local.returnXML = local.libCFC.getEmailTemplatesXML(siteID=arguments.event.getValue('mc_siteinfo.siteID'), treeCode=arguments.event.getValue('_ettreecode'), gridExt=arguments.event.getValue('_etg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="editTemplate">
						<cfset local.returnString = local.libCFC.editTemplate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), templateID=arguments.event.getValue('templateID',0), treeCode=arguments.event.getValue('_ettreecode'), gridExt=arguments.event.getValue('_etg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editCategory">
						<cfset local.returnString = local.libCFC.editCategory(categoryID=arguments.event.getValue('categoryID',0), treeCode=arguments.event.getValue('_ettreecode'), gridExt=arguments.event.getValue('_etg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="contributions">
				<cfset local.libCFC = createObject("component","model.admin.contributions.contributionAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editCampaign">
						<cfset local.returnString = local.libCFC.editCampaign(siteID=arguments.event.getValue('siteID'), programID=arguments.event.getValue('programID'), campaignID=arguments.event.getValue('campaignID'), ahrLink=arguments.event.getValue('mc_adminNav.adminHomeResource'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="glAccountLimits">
				<cfset local.libCFC = createObject("component","model.admin.GLAccounts.GLAccountsAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editListLimitSchedule">
						<cfset local.returnString = local.libCFC.editListLimitSchedule(orgid=arguments.event.getValue('mc_siteinfo.orgid'), scheduleID=arguments.event.getValue('scheduleID'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="glAccountWidget">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addGL">
						<cfset local.returnString = local.libCFC.addGL(orgid=arguments.event.getValue('mc_siteinfo.orgid'), rptid=arguments.event.getValue('_glrptid'), 
							csrid=arguments.event.getValue('_glcsrid'), gridExt=arguments.event.getValue('_glgext'), extraNodeName=arguments.event.getValue('_glextnodename'), 
							glatid=arguments.event.getValue('_glglatid'), widgetMode=arguments.event.getValue('_glmode','report'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="accountListSelectorXML">
						<cfset local.returnString = local.libCFC.accountListSelectorXML(orgid=arguments.event.getValue('mc_siteinfo.orgid'), rptid=arguments.event.getValue('_glrptid'), 
							glidlist=arguments.event.getValue('_glidlist',''), extraNodeName=arguments.event.getValue('_glextnodename'), glatid=arguments.event.getValue('_glglatid'), 
							kw=arguments.event.getTrimValue('_glkw',''), widgetMode=arguments.event.getValue('_glmode','report'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="massEmails">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.massEmails.massEmails")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="sendMassEmails">
						<cfset local.returnString = local.libCFC.sendMassEmails(event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="referralPanelWidget">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.referralPanelWidget.referralPanelWidget")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addReferralPanel">
						<cfset local.returnString = local.libCFC.addReferralPanel(siteid=arguments.event.getValue('mc_siteinfo.siteid'), itemid=arguments.event.getValue('_refitemid'), 
							csrid=arguments.event.getValue('_refcsrid'), gridExt=arguments.event.getValue('_refgext'), extraNodeName=arguments.event.getValue('_refextnodename'), 
							widgetMode=arguments.event.getValue('_refmode','report'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="subscriptionWidget">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.subscriptionWidget.subscriptionWidget")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="subconditionsXML">
						<cfset local.returnString = local.libCFC.subconditionsXML(rptid=arguments.event.getValue('_subsrptid'), 
							csrid=arguments.event.getValue('_subscsrid'), gridExt=arguments.event.getValue('_subsgext'), 
							filterMode=arguments.event.getValue('_subsfmode'), showIcons=arguments.event.getValue('rohi',0) is not 1)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="addSubCondition">
						<cfset local.returnString = local.libCFC.addSubCondition(rptid=arguments.event.getValue('_subsrptid'), 
							csrid=arguments.event.getValue('_subscsrid'), csetid=arguments.event.getValue('_subscsetid'),
							gridExt=arguments.event.getValue('_subsgext'), excludeSteps=arguments.event.getValue('_subsexcludesteps'), 
							filterMode=arguments.event.getValue('_subsfmode'), step=arguments.event.getValue('_substep','t'), 
							f_T=arguments.event.getValue('f_T',''), f_S=arguments.event.getValue('f_S',''), f_R=arguments.event.getValue('f_R',''), 
							f_SS=arguments.event.getValue('f_SS',''), f_PS=arguments.event.getValue('f_PS',''), f_PM=arguments.event.getValue('f_PM',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editSubCondition">
						<cfset local.returnString = local.libCFC.editSubCondition(rptid=arguments.event.getValue('_subsrptid'), 
							csrid=arguments.event.getValue('_subscsrid'), cid=arguments.event.getValue('_subscid'),
							gridExt=arguments.event.getValue('_subsgext'), excludeSteps=arguments.event.getValue('_subsexcludesteps'), 
							filterMode=arguments.event.getValue('_subsfmode'), step=arguments.event.getValue('_substep','t'), 
							f_T=arguments.event.getValue('f_T',''), f_S=arguments.event.getValue('f_S',''), f_R=arguments.event.getValue('f_R',''), 
							f_SS=arguments.event.getValue('f_SS',''), f_PS=arguments.event.getValue('f_PS',''), f_PM=arguments.event.getValue('f_PM',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editSubConditionSet">
						<cfset local.returnString = local.libCFC.editSubConditionSet(rptid=arguments.event.getValue('_subsrptid'), 
							csrid=arguments.event.getValue('_subscsrid'), csetid=arguments.event.getValue('_subscsetid'),
							gridExt=arguments.event.getValue('_subsgext'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="eventWidget">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.eventWidget.eventWidget")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addEvent">
						<cfset local.returnString = local.libCFC.addEvent(siteid=arguments.event.getValue('mc_siteinfo.siteid'), rptid=arguments.event.getValue('_evrptid'), 
							csrid=arguments.event.getValue('_evcsrid'), gridExt=arguments.event.getValue('_evgext'), 
							filterMode=arguments.event.getValue('_evfiltermode'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="memberHistoryWidget">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.memberHistoryWidget.memberHistoryWidget")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="addMemberHistorySection">
						<cfset local.returnString = local.libCFC.addMemberHistorySection(rptid=arguments.event.getValue('_mhrptid'), csrid=arguments.event.getValue('_mhcsrid'), 
							typeid=arguments.event.getValue('_mhtypeid'), gridExt=arguments.event.getValue('_mhgext'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="contentDetails">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.contentDetails.contentDetails")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="getContentVersion">
						<cfset local.returnString = local.libCFC.getContentVersion(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
							contentID=arguments.event.getValue('cID',0), languageID=arguments.event.getValue('clID',0),
							contentVersionID=arguments.event.getValue('cvID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editMetaData">
						<cfset local.returnString = local.libCFC.editMetaData(contentID=arguments.event.getValue('cID',0), languageID=arguments.event.getValue('lID',0),
							rID=arguments.event.getValue('rID',0), zoneName=arguments.event.getValue('zoneName',''), addNew=arguments.event.getValue('addNew',true),
							actionMode=arguments.event.getValue('actionMode',''), extraParams=arguments.event.getValue('extraParams',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="showEditor">
						<cfset local.returnString = local.libCFC.showEditor(contentID=arguments.event.getValue('cID',0), languageID=arguments.event.getValue('clID',0), 
							contentVersionID=arguments.event.getValue('cvID',0), rID=arguments.event.getValue('rID',0), editorDisplayMode='admin', zoneName=arguments.event.getValue('zoneName',''),
							actionMode=arguments.event.getValue('actionMode',''), extraParams=arguments.event.getValue('extraParams',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="webEditor">
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="getStylesXML">
						<cfset local.returnXML = application.objWebEditor.getStyles_CK()>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="massBadges">
				<cfset local.libCFC = createObject("component","model.admin.badges.massPrintBadges")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="printMassBadges">
						<cfset local.returnString = local.libCFC.printMassBadges(event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="resourceTemplates">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editResourceTemplate">
						<cfset local.returnString = local.libCFC.editResourceTemplate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), libraryID=arguments.event.getValue('libraryID',0),
									templateID=arguments.event.getValue('templateID',0), resourceType=arguments.event.getValue('_rtresourceType'),
									gridExt=arguments.event.getValue('_rtg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="copyResourceTemplate">
						<cfset local.returnString = local.libCFC.copyResourceTemplate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), templateID=arguments.event.getValue('copyFromTemplateID',0), 
									resourceType=arguments.event.getValue('_rtresourceType'), gridExt=arguments.event.getValue('_rtg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editResourceTemplateLibrary">
						<cfset local.returnString = local.libCFC.editResourceTemplateLibrary(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									libraryID=arguments.event.getValue('libraryID',0), resourceType=arguments.event.getValue('_rtresourceType'), 
									gridExt=arguments.event.getValue('_rtg'), displayMode=arguments.event.getValue('displayMode','default'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editCategoryTree">
						<cfset local.returnString = local.libCFC.editCategoryTree(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									libraryID=arguments.event.getValue('libraryID',0), categoryTreeID=arguments.event.getValue('ctID',0),
									resourceType=arguments.event.getValue('_rtresourceType'), gridExt=arguments.event.getValue('_rtg'),
									createdFlag=arguments.event.getValue('createdFlag',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="editCategory">
						<cfset local.returnString = local.libCFC.editCategory(categoryTreeID=arguments.event.getValue('ctID',0),
							categoryID=arguments.event.getValue('cID',0), controllingSiteResourceID=arguments.event.getValue('csrID',0),
							event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="massAddCategories">
						<cfset local.returnString = local.libCFC.massAddCategories(categoryTreeID=arguments.event.getValue('ctID',0),
							controllingSiteResourceID=arguments.event.getValue('csrID',0), event=arguments.event)>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="loadResourceTemplatePreviousVersions">
						<cfset local.returnString = local.libCFC.loadResourceTemplatePreviousVersions(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									templateID=arguments.event.getValue('templateID',0), resourceType=arguments.event.getValue('_rtresourceType'), gridExt=arguments.event.getValue('_rtg'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="loadResourceItemToTest">
						<cfset local.returnString = local.libCFC.loadResourceItemToTest(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
									siteID=arguments.event.getValue('mc_siteinfo.siteID'), templateID=arguments.event.getValue('templateID',0), 
									resourceType=arguments.event.getValue('_rtresourceType'), gridExt=arguments.event.getValue('_rtg'),templateType=arguments.event.getValue('templateType'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="renderResourceTemplate">
						<cfset local.returnString = local.libCFC.renderResourceTemplate(templateContent=arguments.event.getValue('templateContent',''), 
									templateTypeCode=arguments.event.getValue('templateTypeCode',''), templateFormat=arguments.event.getValue('templateFormat',''), 
									resourceJSON=arguments.event.getValue('resourceJSON',''), resourceType=arguments.event.getValue('_rtresourceType'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="getResourceTemplateContent">
						<cfset local.returnString = local.libCFC.getResourceTemplateContent(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
									templateID=arguments.event.getValue('templateID',0), resourceType=arguments.event.getValue('_rtresourceType'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="featuredImages">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editFeaturedImage">
						<cfset local.returnString = local.libCFC.editFeaturedImage(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), controllingReferenceID=arguments.event.getValue('_crid',0), controllingReferenceType=arguments.event.getValue('_crtype',''), referenceID=arguments.event.getValue('_rid',0), referenceType=arguments.event.getValue('_rtype',''), resourceType=arguments.event.getValue('_resType',''), resourceTypeTitle=arguments.event.getValue('_title',''), onDeleteImageHandler=arguments.event.getValue('_delRetFunc',''), onSaveImageHandler=arguments.event.getValue('_saveRetFunc',''), featuredImageExt=arguments.event.getValue('_ftdext',''), dependentControllingReferenceID=arguments.event.getValue('_dcrid',0), dependentControllingReferenceType=arguments.event.getValue('_dcrtype',''), dependentReferenceID=arguments.event.getValue('_drid',0), dependentReferenceType=arguments.event.getValue('_drtype',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="saveFeaturedImage">
						<cfset local.returnString = local.libCFC.saveFeaturedImage(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), featuredImage=arguments.event.getValue('featuredImage',''), controllingReferenceID=arguments.event.getValue('controllingReferenceID',0), controllingReferenceType=arguments.event.getValue('controllingReferenceType',''), referenceID=arguments.event.getValue('referenceID',0), referenceType=arguments.event.getValue('referenceType',''), featureImageConfigID=arguments.event.getValue('featureImageConfigID',0), onSaveImageHandler=arguments.event.getValue('onSaveImageHandler',''), featuredImageExt=arguments.event.getValue('featuredImageExt',''), strDependentFtdImgSettings=local.libCFC.getDependentFeaturedImageStructure(event=arguments.event))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="renderFeaturedImageSelector">
						<cfset local.returnString = local.libCFC.renderFeaturedImageSelector(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), 
							controllingReferenceID=arguments.event.getValue('_fcrid'), controllingReferenceType=arguments.event.getValue('_fcrtype'), referenceID=arguments.event.getValue('_frid'), 
							referenceType=arguments.event.getValue('_frtype'), resourceType=arguments.event.getValue('_frestype'), resourceTypeTitle=arguments.event.getValue('_frestypetitle'),
							saveImageHandler=arguments.event.getValue('_fsaveimghandler',''), deleteImageHandler=arguments.event.getValue('_fdeleteimghandler',''), 
							header=arguments.event.getValue('_fhdr',''), ftdImgClassList=arguments.event.getValue('_fcls',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="saveFeaturedImageInline">
						<cfset local.returnString = local.libCFC.saveFeaturedImageInline(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), featuredImageFile=arguments.event.getValue('featuredImageFile',''), referenceID=arguments.event.getValue('refid',0), referenceType=arguments.event.getValue('reftype',''), featureImageConfigID=arguments.event.getValue('ficid',0), dependentReferenceID=arguments.event.getValue('drefid',0), dependentReferenceType=arguments.event.getValue('dreftype',''), dependentFeatureImageConfigID=arguments.event.getValue('dficid',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="siteResourceFields">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.siteResourceFields.siteResourceFields")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editField">
						<cfset local.returnString = local.libCFC.editField(siteID=arguments.event.getValue('mc_siteinfo.siteID'), columnID=arguments.event.getValue('columnID',0), siteResourceID=arguments.event.getValue('srid',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
					<cfcase value="renderFields">
						<cfset local.returnString = local.libCFC.renderFields(siteID=arguments.event.getValue('mc_siteinfo.siteID'), siteResourceID=arguments.event.getValue('srid',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="listAdmin">
				<cfset local.libCFC = createObject("component","model.admin.list.listAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="downloadMessageAttachment">
						<cfset local.returnString = local.libCFC.downloadMessageAttachment(siteID=arguments.event.getValue('mc_siteinfo.siteID'),messageID=arguments.event.getValue('mID',0), attachmentID=arguments.event.getValue('aID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			
			<cfcase value="smsTemplates">
				<cfset local.libCFC = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate")>
				<cfswitch expression="#arguments.event.getValue('mca_ajaxfunc','')#">
					<cfcase value="editSMSTemplate">
						<cfset local.returnString = local.libCFC.editSMSTemplate(siteID=arguments.event.getValue('mc_siteinfo.siteID'), templateID=arguments.event.getValue('templateID',0), treeCode=arguments.event.getValue('_ettreecode'), gridExt=arguments.event.getValue('_etg'), referralID=arguments.event.getValue('referralID'), triggerID=arguments.event.getValue('triggerID'), usageTypeID=arguments.event.getValue('usageTypeID'))>
						<cfset local.returnStruct = returnAppStruct(local.returnString,"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
		</cfswitch>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="runJSONRequest" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="jsonlib" type="string" required="TRUE">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfswitch expression="#arguments.jsonlib#">
			<cfcase value="events">
				<cfset local.libCFC = createObject("component","model.admin.events.event")>
				<cfset local.libCalendarCFC = createObject("component","model.events.calendar")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getAllCategoriesForCalendar">
						<cfset local.returnXML = local.libCFC.getCategoriesForCalendar(calID=arguments.event.getValue('calID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"echo")>
					</cfcase>
					<cfcase value="getCategoriesForSyndication">
						<cfset local.returnXML = local.libCFC.getCategoriesForSyndication(siteID=arguments.event.getValue('mc_siteinfo.siteID'), calID=arguments.event.getValue('calID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="getEventsForSyndication">
						<cfset local.returnXML = local.libCFC.getEventsForSyndication(calID=arguments.event.getValue('calID',0),catID=arguments.event.getValue('catID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="getCategoriesForCalendar">
						<cfset local.returnXML = local.libCalendarCFC.getCategoriesForCalendar(calendarID=arguments.event.getValue('calID',0),languageID=arguments.event.getValue('mc_siteinfo.defaultLanguageID'))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="getEventsForAssetCalendar">
						<cfset local.returnArray = local.libCFC.getEventsForAssetCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteID'), start=arguments.event.getValue('start',0), end=arguments.event.getValue('end',0), assetsCategoryList=arguments.event.getValue('assetCtID',''))>
						<cfset local.returnStruct = returnAppStruct(local.returnArray,"echo")>
					</cfcase>
					<cfcase value="flagRegistrant">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.flagRegistrant(registrantID=arguments.event.getValue('registrantID',0)),"echo")>
					</cfcase>
					<cfcase value="unflagRegistrant">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.unflagRegistrant(registrantID=arguments.event.getValue('registrantID',0)),"echo")>
					</cfcase>
					<cfcase value="getRateGroupingData">
						<cfset local.returnXML = local.libCFC.getRateGrouping(rateGroupingID=arguments.event.getValue('rgID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"echo")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="memHistory">
				<cfset local.libCFC = createObject("component","model.admin.memberHistory.memberHistory")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getSubcategoriesForCategory">
						<cfset local.returnXML = local.libCFC.getSubcategoriesForCategory(catID=arguments.event.getValue('catID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="subs">
				<cfset local.libCFC = createObject("component","model.admin.subscriptions.subscriptions")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getSubscriptionsForSubType">
						<cfset local.strArgs = { "typeid"=arguments.event.getValue('typeid',0) }>
						<cfif arguments.event.valueExists('soldspt')><cfset local.strArgs['soldSeparately'] = val(arguments.event.getValue('soldspt'))></cfif>
						<cfset local.returnJSON = local.libCFC.getSubscriptionsForSubType(argumentCollection=local.strArgs)>
						<cfset local.returnStruct = returnAppStruct(local.returnJSON,"echo")>
					</cfcase>
					<cfcase value="getSubRatesForSub">
						<cfset local.returnJSON = local.libCFC.getSubRatesForSub(subid=arguments.event.getValue('subid',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnJSON,"echo")>
					</cfcase>
					<cfcase value="getSubscriptionsForMemberDate">
						<cfset local.returnJSON = local.libCFC.getSubscriptionsForMemberDate(typeID=arguments.event.getValue('typeID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnJSON,"echo")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="accounting">
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">	
					<cfcase value="getBatchNames">					
						<cfset local.returnArray = createObject("component","model.admin.reports.accounting.Payments").getBatchNames(orgid=arguments.event.getValue('mc_siteinfo.orgid'),datefrom=arguments.event.getValue('df',''),dateto=arguments.event.getValue('dt',''), term=arguments.event.getValue('term',''))>
						<cfset local.returnStruct = returnAppStruct(serializeJSON(local.returnArray),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>		
			<cfcase value="ets">
				<cfset local.libCFC = createObject("component","model.admin.emailtemplates.emailtemplates")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getEmailTemplatesForCategory">
						<cfset local.returnXML = local.libCFC.getEmailTemplatesForCategory(catid=arguments.event.getValue('catid',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"echo")>
					</cfcase>
				</cfswitch>							
			</cfcase>
			<cfcase value="referral">
				<cfset local.libCFC = createObject("component","model.admin.referrals.referrals")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getSubPanels">
						<cfset local.returnXML = local.libCFC.getSubPanelsByID(panelid=arguments.event.getValue('panelid',0), isActive=arguments.event.getValue('isActive',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="emailBlast">
				<cfset local.libCFC = createObject("component","model.admin.emailBlast.emailBlast")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getSubcategoriesForCategory">
						<cfset local.returnXML = local.libCFC.getSubcategoriesForCategory(catID=arguments.event.getValue('catID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="getBlastsForCalendar">
						<cfset local.returnArray = local.libCFC.getBlastsForCalendar(
							siteID=arguments.event.getValue('mc_siteinfo.siteID'),
							orgID=arguments.event.getValue('mc_siteinfo.orgID'),
							categoryID=arguments.event.getValue('categoryID',0),
							subCategoryIDList=arguments.event.getValue('subCategoryIDList',''),
							fBlastKeyword=arguments.event.getValue('fBlastKeyword',''),
							fBlastMessage=arguments.event.getValue('fBlastMessage',''),
							fBlastRecurring=arguments.event.getValue('fBlastRecurring',0),
							fBlastTestMsg=arguments.event.getValue('fBlastTestMsg',0),
							dsp=arguments.event.getValue('dsp','all'),
							start=arguments.event.getValue('start',0),
							end=arguments.event.getValue('end',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnArray,"echo")>
					</cfcase>
				</cfswitch>			
			</cfcase>	
			<cfcase value="lists">
				<cfset local.libCFC = createObject("component","model.admin.list.list")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getDatesForList">
						<cfset local.returnXML = local.libCFC.getDatesForList(listName=arguments.event.getValue('listName',''), siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID)>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="getListsForSite">
						<cfset local.returnArray = local.libCFC.getListsForSiteByOrg(orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)>
						<cfset local.returnStruct = returnAppStruct(serializeJSON(local.returnArray),"echo")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="memberimport">
				<cfset local.libCFC = createObject("component","model.admin.import.importAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="continueImport">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.continueImport(jobID=arguments.event.getValue('jID',0), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
							logID=arguments.event.getValue('lid',0), referenceType=arguments.event.getValue('referenceType',''), referenceID=arguments.event.getValue('referenceID',0)),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="deploy">
				<cfset local.libCFC = createObject("component","model.admin.website.websiteAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="populateDeployNode">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.populateDeployNode(s=arguments.event.getTrimValue('s','')),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="dashboard">
				<cfset local.libCFC = createObject("component","model.admin.desktop.desktopAdmin")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getSBStatus">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getSBStatus(),"echo")>
					</cfcase>
					<cfcase value="activateSBQueue">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.activateSBQueue(queue=arguments.event.getTrimValue('queue','')),"echo")>
					</cfcase>
					<cfcase value="getSBQueueSummary">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getSBQueueSummary(),"echo")>
					</cfcase>
					<cfcase value="getMCGQueueSummary">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getMCGQueueSummary(thisOrgID=arguments.event.getValue('mc_siteinfo.orgID')),"echo")>
					</cfcase>
					<cfcase value="getMCGQueueEntries">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getMCGQueueEntries(thisOrgID=arguments.event.getValue('mc_siteinfo.orgID')),"echo")>
					</cfcase>
					<cfcase value="MCGQueueAction">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.MCGQueueAction(thisOrgCode=arguments.event.getValue('mc_siteinfo.orgCode'),
							queue=arguments.event.getTrimValue('queue',''), status=arguments.event.getTrimValue('status',''), action=arguments.event.getTrimValue('action',''),
							orgcode=arguments.event.getTrimValue('orgcode',''), processtype=arguments.event.getTrimValue('processtype','')),"echo")>
					</cfcase>
					<cfcase value="SBQueueAction">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.SBQueueAction(queue=arguments.event.getTrimValue('queue',''), 
							status=arguments.event.getTrimValue('status',''), action=arguments.event.getTrimValue('action','')),"echo")>
					</cfcase>
					<cfcase value="getSBLog">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getSBLog(),"echo")>
					</cfcase>
					<cfcase value="truncateSBLog">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.truncateSBLog(),"echo")>
					</cfcase>
					<cfcase value="refreshOrgConditions">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.refreshOrgConditions(orgID=arguments.event.getTrimValue('o',''), 
							refreshAction=arguments.event.getTrimValue('oa','')),"echo")>
					</cfcase>
					<cfcase value="refreshWebsiteConfFiles">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.refreshWebsiteConfFiles(siteID=arguments.event.getTrimValue('s','')),"echo")>
					</cfcase>
					<cfcase value="checkSiteDirectories">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.checkSiteDirectories(siteCode=arguments.event.getTrimValue('s','')),"echo")>
					</cfcase>
					<cfcase value="createAppDirectories">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.createAppDirectories(),"echo")>
					</cfcase>
					<cfcase value="triggerClusterWideResetAppVars">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.triggerClusterWideResetAppVars(),"echo")>
					</cfcase>
					<cfcase value="reprocessAllPrints">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.reprocessAllPrints(orgID=arguments.event.getTrimValue('o',0)),"echo")>
					</cfcase>
					<cfcase value="triggerClusterWideReloadSiteInfo">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.triggerClusterWideReloadSiteInfo(),"echo")>
					</cfcase>
					<cfcase value="clearAdhocMemory">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.clearAdhocMemory(),"echo")>
					</cfcase>
					<cfcase value="disableBetaAccess">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.disableBetaAccess(siteCode=arguments.event.getTrimValue('s','')),"echo")>
					</cfcase>
					<cfcase value="filterCondCacheLogResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterCondCacheLogResult(procName=arguments.event.getTrimValue('procName',''),dateFrom=arguments.event.getTrimValue('dateFrom',''),dateTo=arguments.event.getTrimValue('dateTo',''),orderBy=arguments.event.getTrimValue('orderBy','')),"echo")>
					</cfcase>
					<cfcase value="getAPITestingStatus">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.getAPITestingStatus(),"echo")>
					</cfcase>
					<cfcase value="filterEventCertsLogResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterEventCertsLogResult(thisSiteCode=arguments.event.getValue('mc_siteinfo.siteCode'), certificateIDList=arguments.event.getTrimValue('certificateIDList',''), dateFrom=arguments.event.getTrimValue('dateFrom',''), dateTo=arguments.event.getTrimValue('dateTo',''), orderBy=arguments.event.getTrimValue('orderBy','')),"echo")>
					</cfcase>
					<cfcase value="filterPMIStatisticsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterPMIStatisticsResult(thisOrgID=arguments.event.getValue('mc_siteinfo.orgID'), thisSiteCode=arguments.event.getValue('mc_siteinfo.siteCode'), procName=arguments.event.getTrimValue('procName',''), dateFrom=arguments.event.getTrimValue('dateFrom',''), dateTo=arguments.event.getTrimValue('dateTo',''), memberCountFrom=arguments.event.getTrimValue('memberCountFrom',''), memberCountTo=arguments.event.getTrimValue('memberCountTo',''),orderBy=arguments.event.getTrimValue('orderBy',''),orgID=arguments.event.getTrimValue('orgID',''),splitByOrgcode=arguments.event.getTrimValue('splitByOrgcode',false)),"echo")>
					</cfcase>
					<cfcase value="filterSQLStatisticsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterSQLStatisticsResult(procName=arguments.event.getTrimValue('procName',''), dateFrom=arguments.event.getTrimValue('dateFrom',''), dateTo=arguments.event.getTrimValue('dateTo',''), orderBy=arguments.event.getTrimValue('orderBy','')),"echo")>
					</cfcase>
					<cfcase value="filterARCacheLogsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterARCacheLogsResult(thisOrgCode=arguments.event.getValue('mc_siteinfo.orgCode'),orgID=arguments.event.getTrimValue('orgID',''), dateFrom=arguments.event.getTrimValue('dateFrom',''), dateTo=arguments.event.getTrimValue('dateTo',''), orderBy=arguments.event.getTrimValue('orderBy','')),"echo")>
					</cfcase>
					<cfcase value="filterMemGrpHistoryLogsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterMemGrpHistoryLogsResult(thisOrgCode=arguments.event.getValue('mc_siteinfo.orgCode'),orgID=arguments.event.getTrimValue('orgID',''), dateFrom=arguments.event.getTrimValue('dateFrom',''), dateTo=arguments.event.getTrimValue('dateTo',''), orderBy=arguments.event.getTrimValue('orderBy','')),"echo")>
					</cfcase>
					<cfcase value="filterDashboardObjectsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterDashboardObjectsResult(thisSiteCode=arguments.event.getValue('mc_siteinfo.siteCode'), siteID=arguments.event.getTrimValue('siteID',0), objectTypeIDList=arguments.event.getTrimValue('objectTypeIDList',''), orderBy=arguments.event.getTrimValue('orderBy',''), count=arguments.event.getTrimValue('count',25)),"echo")>
					</cfcase>
					<cfcase value="filterDashboardStatsResult">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.filterDashboardStatsResult(thisSiteCode=arguments.event.getValue('mc_siteinfo.siteCode'), siteID=arguments.event.getTrimValue('siteID',0), statsMode=arguments.event.getTrimValue('statsMode',''), count=arguments.event.getTrimValue('count',25)),"echo")>
					</cfcase>
					<cfcase value="addSessionToKillList">
						<cfset local.returnStruct =  returnAppStruct(local.libCFC.addSessionToKillList(cfid=arguments.event.getTrimValue('mc_killCFID','')),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="nav">
				<cfset local.libCFC = createObject("component","model.admin.navigation.navigation")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="getResourceFnForResourceType">
						<cfset local.returnXML = local.libCFC.getResourceFnForResourceType(resourceTypeID=arguments.event.getValue('resourceTypeID',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"xml")>
					</cfcase>
					<cfcase value="saveAdminTool">
						<cfset local.returnStruct = returnAppStruct(local.libCFC.saveAdminTool(event=arguments.event),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			<cfcase value="grps">
				<cfset local.libCFC = createObject("component","model.admin.groups.groups")>
				<cfswitch expression="#arguments.event.getValue('mca_jsonfunc','')#">
					<cfcase value="refreshGroups">
						<cfset local.returnXML = local.libCFC.doRefreshGroups(orgid=arguments.event.getValue('mc_siteinfo.orgid'),hideProtected=arguments.event.getValue('hideProtected',0))>
						<cfset local.returnStruct = returnAppStruct(local.returnXML,"echo")>
					</cfcase>
				</cfswitch>			
			</cfcase>
			<cfcase value="mcdatatable">
				<cfswitch expression="#arguments.event.getValue('com','')#">
					<cfcase value="aajImportJSON">
						<cfset local.objCom = createObject("component","model.admin.aajImport.aajImportJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="ABACasesJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.aba.aba.casesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="ACBARadInvJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.acbar.acbar.adInvoicingJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="adminPermissionsJSON">
						<cfset local.objCom = createObject("component","model.admin.adminPermissions.adminPermissionsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="adsJSON">
						<cfset local.objCom = createObject("component","model.admin.ads.adsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="advanceFormulasJSON">
						<cfset local.objCom = createObject("component","model.admin.advanceFormulas.advanceFormulasJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="aliasJSON">
						<cfset local.objCom = createObject("component","model.admin.alias.aliasJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="announcementsJSON">
						<cfset local.objCom = createObject("component","model.admin.announcements.announcementsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="batchJSON">
						<cfset local.objCom = createObject("component","model.admin.transactions.batchJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="blogJSON">
						<cfset local.objCom = createObject("component","model.admin.blogs.blogJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="carouselsJSON">
						<cfset local.objCom = createObject("component","model.admin.carousels.carouselsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="categoriesJSON">
						<cfset local.objCom = createObject("component","model.admin.categories.categoriesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="communitiesJSON">
						<cfset local.objCom = createObject("component","model.admin.community.communitiesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="contentJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.contentDetails.contentJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="contributionJSON">
						<cfset local.objCom = createObject("component","model.admin.contributions.contributionJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="couponJSON">
						<cfset local.objCom = createObject("component","model.admin.coupons.couponJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="cssJSON">
						<cfset local.objCom = createObject("component","model.admin.css.cssJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="customJSON">
						<cfset local.objCom = createObject("component","model.admin.customFields.customFieldsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="customFieldsJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.customFields.customFieldsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="customPageJSON">
						<cfset local.objCom = createObject("component","model.admin.customPages.customPageJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="depoConnectJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.mc.ts.depoConnectJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="desktopJSON">
						<cfset local.objCom = createObject("component","model.admin.desktop.desktopJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="deviceJSON">
						<cfset local.objCom = createObject("component","model.admin.badges.deviceJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="documentJSON">
						<cfset local.objCom = createObject("component","model.admin.documents.documentJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="documentDetailsJSON"> 
						<cfset local.objCom = createObject("component","model.admin.common.modules.documentDetails.documentDetailsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="eagleProgramSplitJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.wa.wa.eagleProgramSplitJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="emailBlastJSON">
						<cfset local.objCom = createObject("component","model.admin.emailBlast.emailBlastJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="emailTemplateJSON">
						<cfset local.objCom = createObject("component","model.admin.emailTemplates.emailTemplateJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="emailPreferencesJSON">
						<cfset local.objCom = createObject("component","model.admin.emailPreferences.emailPreferencesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="evaluationsJSON">
						<cfset local.objCom = createObject("component","model.admin.evaluations.evaluationsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="eventsJSON">
						<cfset local.objCom = createObject("component","model.admin.events.eventsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="fileShareJSON">
						<cfset local.objCom = createObject("component","model.admin.fileShare.fileShareJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="GLAccountsJSON">
						<cfset local.objCom = createObject("component","model.admin.GLAccounts.GLAccountsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="glAccountWidgetJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidgetJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="groupsJSON">
						<cfset local.objCom = createObject("component","model.admin.groups.groupsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="importJSON">
						<cfset local.objCom = createObject("component","model.admin.import.importJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="invoiceJSON">
						<cfset local.objCom = createObject("component","model.admin.transactions.invoiceJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="invoiceProfilesJSON">
						<cfset local.objCom = createObject("component","model.admin.invoiceProfiles.invoiceProfilesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="jobBankJSON">
						<cfset local.objCom = createObject("component","model.admin.jobBank.jobBankJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="listsJSON">
						<cfset local.objCom = createObject("component","model.admin.list.listsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="massEmailsJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.massEmails.massEmailsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberJSON">
						<cfset local.objCom = createObject("component","model.admin.members.memberJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberDirectoryJSON">
						<cfset local.objCom = createObject("component","model.admin.memberDirectory.memberDirectoryJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberFieldSetsJSON">
						<cfset local.objCom = createObject("component","model.admin.memberFieldSets.memberFieldSetsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberGroupSetsJSON">
						<cfset local.objCom = createObject("component","model.admin.memberGroupSets.memberGroupSetsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberHistoryJSON">
						<cfset local.objCom = createObject("component","model.admin.memberHistory.memberHistoryJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberSettingsJSON">
						<cfset local.objCom = createObject("component","model.admin.memberSettings.memberSettingsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="memberUpdateJSON">
						<cfset local.objCom = createObject("component","model.admin.memberUpdate.memberUpdateJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="menuJSON">
						<cfset local.objCom = createObject("component","model.admin.menu.menuJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="merchantProfilesJSON">
						<cfset local.objCom = createObject("component","model.admin.merchantProfiles.merchantProfilesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="MyCEJSON">
						<cfset local.objCom = createObject("component","model.admin.MyCE.MyCEJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="MyPageJSON">
						<cfset local.objCom = createObject("component","model.admin.MyPage.MyPageJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="navigationJSON">
						<cfset local.objCom = createObject("component","model.admin.navigation.navigationJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="networkGeneratorJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.indy.indy.networkGeneratorJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="organizationJSON">
						<cfset local.objCom = createObject("component","model.admin.organization.organizationJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="pageJSON">
						<cfset local.objCom = createObject("component","model.admin.pages.pageJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="permissionJSON">
						<cfset local.objCom = createObject("component","model.admin.permissions.permissionJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="publicationJSON">
						<cfset local.objCom = createObject("component","model.admin.publications.publicationJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="photoGalleryJSON">
						<cfset local.objCom = createObject("component","model.admin.photoGallery.photoGalleryJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="postTypeJSON">
						<cfset local.objCom = createObject("component","model.admin.postTypes.postTypeJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="projectJSON">
						<cfset local.objCom = createObject("component","model.admin.projects.projectJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="referralsJSON">
						<cfset local.objCom = createObject("component","model.admin.referrals.referralsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="reportsJSON">
						<cfset local.objCom = createObject("component","model.admin.reports.reportsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="resourceTemplateJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplateJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="rssFeedsJSON">
						<cfset local.objCom = createObject("component","model.admin.rss.rssFeedsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="ruleBuilderJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilderJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="savedReportsJSON">
						<cfset local.objCom = createObject("component","model.admin.reports.savedReports.savedReportsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="searchJSON">
						<cfset local.objCom = createObject("component","model.admin.search.searchJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="seminarWebJSON">
						<cfset local.objCom = createObject("component","model.admin.seminarWeb.seminarWebJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="siteResourceFieldsJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.siteResourceFields.siteResourceFieldsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="storeJSON">
						<cfset local.objCom = createObject("component","model.admin.store.storeJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="subscriptionJSON">
						<cfset local.objCom = createObject("component","model.admin.subscriptions.subscriptionJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="tasksJSON">
						<cfset local.objCom = createObject("component","model.admin.tasks.tasksJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="taxProfilesJSON">
						<cfset local.objCom = createObject("component","model.admin.taxProfiles.taxProfilesJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="TNBARSuccessJSON">
						<cfset local.objCom = createObject("component","model.admin.custom.tnbar.tnbar.TNBARsuccessJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="videoGalleryJSON">
						<cfset local.objCom = createObject("component","model.admin.videoGallery.videoGalleryJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="virtualGroupsJSON">
						<cfset local.objCom = createObject("component","model.admin.virtualGroups.virtualGroupsJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="websiteJSON">
						<cfset local.objCom = createObject("component","model.admin.website.websiteJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="templateJSON">
						<cfset local.objCom = createObject("component","model.admin.template.templateJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
					<cfcase value="smsTemplateJSON">
						<cfset local.objCom = createObject("component","model.admin.common.modules.smsTemplate.smsTemplateJSON")>
						<cfset local.returnStruct = returnAppStruct(local.objCom.controller(arguments.event),"echo")>
					</cfcase>
				</cfswitch>
			</cfcase>
			</cfswitch>	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="showResourceStatusLegend" access="public" output="True" returnType="void" hint="shows active/pending/deleted ledgend">
		<cfargument name="resourceList" type="string" required="true">
		<cfargument name="responsive" type="boolean" required="false" default="0">

		<style>
			.mcstatus_active{ font-size:8pt;color:##222222; border:1px solid ##dddddd; padding:1px 5px 1px 5px; }
			.mcstatus_inactive{ font-size:8pt;color:##222222; background-color:##e8aeae; border:1px solid ##e77979; padding:1px 5px 1px 5px; }
			.mcstatus_deleted{ font-size:8pt;color:##aaaaaa; background-color:##dddddd; border:1px solid ##bbbbbb; padding:1px 5px 1px 5px; }
		</style>

		<cfsavecontent variable="local.returnData">
			<cfoutput>
				<cfif listFindNoCase(arguments.resourceList,'active')>
					<span class="mcstatus_active">Active</span>
				</cfif>
				<cfif listFindNoCase(arguments.resourceList,'published')>
					<span class="mcstatus_active">Published</span>
				</cfif>
				<cfif listFindNoCase(arguments.resourceList,'inactive')>
					<span class="mcstatus_inactive">Inactive</span>
				</cfif>
				<cfif listFindNoCase(arguments.resourceList,'nonpublished')>
					<span class="mcstatus_inactive">Non-Published</span>
				</cfif>
				<cfif listFindNoCase(arguments.resourceList,'pending')>
					<span class="mcstatus_inactive">Pending</span>
				</cfif>
				<cfif listFindNoCase(arguments.resourceList,'deleted')>
					<span class="mcstatus_deleted">Deleted</span>
				</cfif>
			</cfoutput>
		</cfsavecontent>		

		<cfif arguments.responsive>
			<cfoutput>#local.returnData#</cfoutput>
		<cfelse>
			<div style="width:690px; align:right;">
				<div style="float:right;margin-top:3px;">
					<cfoutput>#local.returnData#</cfoutput>
				</div>
			</div>
		</cfif>
	</cffunction>

	<cffunction name="buildNav4" access="public" returntype="string" output="false">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.mca_s = arguments.event.getValue('mca_s')>
		<cfset local.mca_a = arguments.event.getValue('mca_a')>
		<cfset local.mca_tt = arguments.event.getValue('mca_tt','')>
		<cfset local.mca_ta = arguments.event.getValue('mca_ta','')>
		<cfset local.mca_hk = arguments.event.getValue('mca_a')>
		
		<cfset local.arrNodes = XMLSearch(application.adminNavigationXML,"//navitem[@navigationID='#local.mca_a#']")>
		<cfset local.navName = local.arrNodes[1].xmlAttributes.navName>
		<cfset local.navDesc = local.arrNodes[1].xmlAttributes.navDesc>
		
		<cfset local.cpCount = 0>
		<cfsavecontent variable="local.returnData">
			<cfoutput>
				<cfif len(local.navDesc)><h3>#local.navDesc#</h3></cfif>
				<br/>				
				<cfloop array="#local.arrNodes[1].xmlChildren#" index="local.navChild">
					<cfif structKeyExists(session.mcastruct.strNavKeys, local.navChild.xmlAttributes.navKey)>
						<cfset local.cpCount = local.cpCount + 1>
						<div class="mb-3">
							<a name="navitem" navitemparent="cp#local.cpCount#" href="#arguments.event.getValue('mc_adminNav.adminHome')#&mca_s=#local.mca_s#&mca_a=#local.navChild.xmlAttributes.navigationID#&mca_tt=#local.navChild.xmlAttributes.toolTypeID#&mca_ta=#local.navChild.xmlAttributes.cfcMethod#&mca_lt=#local.mca_a#"><strong>#local.navChild.xmlAttributes.navName#</strong></a>
							<cfif len(local.navChild.xmlAttributes.navDesc)>
								<div class="my-1 ml-4">
									#local.navChild.xmlAttributes.navDesc#
								</div>
							</cfif>
						</div>
					</cfif>
				</cfloop>
			</cfoutput>
		</cfsavecontent>		
		<cfreturn local.returnData>
	</cffunction>
</cfcomponent>
