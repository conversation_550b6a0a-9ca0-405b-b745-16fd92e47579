<cfset local.strFilters = attributes.data.strFilters>
<cfset local.strBrowse = attributes.data.strBrowse>

<cfset local.availableFormats = "">
<cfif attributes.data.semWeb.qrySWP.isConf>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'conf')>
</cfif>
<cfif attributes.data.semWeb.qrySWP.isSWL>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swl')>
</cfif>
<cfif attributes.data.semWeb.qrySWP.isSWOD>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swod')>
</cfif>
<cfif (attributes.data.semWeb.qrySWP.isSWL OR attributes.data.semWeb.qrySWP.isSWOD) and attributes.data.hasActiveBundles>
	<cfset local.availableFormats = listAppend(local.availableFormats, 'swb')>
</cfif>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>
<cfset local.sortOptionsArr = arrayNew(1)>
<cfset arrayAppend(local.sortOptionsArr, { key="date", value="Sort by Date", isSelected = local.strFilters.sortoption eq 'date' })>
<cfset arrayAppend(local.sortOptionsArr, { key="nameasc", value="Sort by Name A-Z", isSelected = local.strFilters.sortoption eq 'nameasc' })>
<cfset arrayAppend(local.sortOptionsArr, { key="namedesc", value="Sort by Name Z-A", isSelected = local.strFilters.sortoption eq 'namedesc' })>
<cfset arrayAppend(local.sortOptionsArr, { key="priceasc", value="Sort by Price Low-High", isSelected = local.strFilters.sortoption eq 'priceasc' })>
<cfset arrayAppend(local.sortOptionsArr, { key="pricedesc", value="Sort by Price High-Low", isSelected = local.strFilters.sortoption eq 'pricedesc' })>
<cfif len(local.strFilters.keywordsList)>
	<cfset arrayAppend(local.sortOptionsArr, { key="rank", value="Relevancy", isSelected = local.strFilters.sortoption eq 'rank' })>
</cfif>
<cfset local.activeSortOptionPos = arrayFind(local.sortOptionsArr, function(item) { if (item.isSelected eq true) return true; return false; })>
<cfif local.activeSortOptionPos eq 0>
	<cfset local.activeSortOptionPos = 1>
</cfif>
<cfset local.activeSortOptionText = local.sortOptionsArr[local.activeSortOptionPos].value>
<cfset local.swCatalogFilterMenuIsClosed = application.mcCacheManager.sessionGetValue(keyname='swCatalogFilterMenuIsClosed', defaultValue=false)>

<cfinclude template="/views/semwebCatalog/responsive/swCatalogcommonCSS.cfm">

<cfsavecontent variable="local.browseProgramsJS">
	<cfoutput>
		<!--- Share This Scripts --->
		<cfif NOT local.isBot>
			<script type="text/javascript">var switchTo5x=true;</script>
			<script type="text/javascript" src="https://ws.sharethis.com/button/buttons.js"></script>
			<script type="text/javascript">stLight.options({publisher:'b8181e8a-537f-4fcd-9253-53d15b7080c3'});</script>
			<script type="text/javascript" src="/assets/common/javascript/videojs/7.7.6/video.min.js"></script>
			<link rel="stylesheet" href="/assets/common/javascript/videojs/7.7.6/video-js.min.css">
		</cfif>

		<script type="text/javascript">
			var #ToScript("#attributes.event.getValue('mainurl')#&panel=browse","swBrowseUrl")#
			var #ToScript("#local.strFilters.URLQueryString#","filtersURLQueryString")#
			
			function initSWBrowsePrograms() {
				if ($('##_swca').length) {
					$("##_swca").multiselect({ multiple:true, noneSelectedText:'Select Credit Authorities', maxWidth:200, header:true, selectedList:1, classes : "swBrowseFilter", position: { my: 'left bottom', at: 'left top'} });
					$("##_swcat").multiselect({ multiple:true, noneSelectedText:'Select Credit Types', maxWidth:200, header:true, selectedList:2, classes : "swBrowseFilter", position: { my: 'left bottom', at: 'left top'} });
					$("##_swcam").multiselect({ multiple:true, noneSelectedText:'Select Credit Amounts', maxWidth:200, header:true, selectedList:3, classes : "swBrowseFilter", position: { my: 'left bottom', at: 'left top'} });

					var arrCreditTypes = '#local.strFilters.creditTypes#'.split(',');
					onChangeSWCreditAuthority(arrCreditTypes);
				}
				if ($('##_sws').length)
					$("##_sws").multiselect({ multiple:true, noneSelectedText:'Select Subjects', maxWidth:200, header:true, selectedList:2, classes : "swBrowseFilter", position: { my: 'left bottom', at: 'left top'} });
				if ($('##_swp').length)
					$("##_swp").multiselect({ multiple:true, noneSelectedText:'Select Publishers', maxWidth:200, header:true, selectedList:1, classes : "swBrowseFilter", position: { my: 'left bottom', at: 'left top'} });

				if ($(window).width() < 768) {
					setTimeout(function(){ $(".ui-multiselect-menu.swBrowseFilter").css("width", $('.keywordSearchBox').width()+"px"); }, 5000);
				}

				var #toScript(local.strBrowse.arrAuthors,"sw_arrauthors")#
				
				if (sw_arrauthors.length) {
					var sw_authorsdata = sw_arrauthors.map(function(r){ return { value:r.authorname, id:r.authorid }; });

					$('##sw_speakers')
						.on( "keydown", function( event ) {
							if ( event.keyCode === $.ui.keyCode.TAB &&
								$( this ).autocomplete( "instance" ).menu.active ) {
									event.preventDefault();
								}
						})
						.autocomplete({
							minLength: 0,
							source:sw_authorsdata,
							focus: function() {
								return false;
							},
							select: function( event, ui ) {
								var nonExistingAID = true;
								var arrAID = $('##_swa').val().split(',');
								for (var j=0; j<arrAID.length;j++) { 
									if(arrAID[j]==ui.item.id) { nonExistingAID = false; break; }
								}
								if (nonExistingAID) {
										arrAID.push(ui.item.id);
										arrAID = arrAID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
									$('##_swa').val(arrAID);

									$('##sw_speakerslist').append('<li id="sw_author'+ui.item.id+'"><a href="##" class="swPrimary" onclick="clearSWAuthorsFilter(this,\'list\','+ui.item.id+');return false;"><i class="bi bi-x" aria-hidden="true"></i> '+ ui.item.value +'</a></li>');
								}

								$('##sw_speakers').val('');
								return false;
							}

						});
				}

				var activeFiltersCount = $('ul.sw_filterSummary li').length;
				$('##swActiveFiltersCount').html(activeFiltersCount + ' active filter' + (activeFiltersCount > 1 ? 's' : ''));
				<cfif NOT local.isBot>
					initSWVideoPreviews();
				</cfif>
			}
			<cfif NOT local.isBot>
				var videoPlayer;
				function initSWVideoPreviews() {
					videoPlayer = videojs('previewPlayer', {
						controls: true,
						autoplay: true,
						preload: 'none',
						height: 350,
					});
					
					$('.swBrowsePrograms .swvideopreview').on('click', function() {
						var videoSRC = $(this).attr('data-swvideopreviewlink');
						var videoDisplayName = $(this).attr('data-swvidepreviewdisplayname');
						videoPlayer.src(videoSRC);
						$('##swVideoPreviewLabel').html(videoDisplayName);
					});

					$('##swVideoPreviewModal').on('hidden', function () {
						videoPlayer.pause();
					});
				}
			</cfif>
			function onChangeSWCreditAuthority(ct) {
				var #toScript(local.strBrowse.arrCreditsInfo,"arrCreditsInfo")#
				var aidlist = $('##_swca').val() || '';
				$('##_swcat').find('option').remove();
				
				if (aidlist != '' && aidlist.length == 1) {
					var arrCreditsInfoFiltered = arrCreditsInfo.filter(function(c){ return c.authorityid == aidlist[0]; });
					if (arrCreditsInfoFiltered.length) {
						strCreditTypes = arrCreditsInfoFiltered[0].strcredittypes;
						
						var arrCreditTypes = $('##_swca').find(':selected').data('swcredittypes').split(',');
						$.each(arrCreditTypes, function (i,item) {
							$('##_swcat').append( $('<option>', { value:item, text:strCreditTypes[item] }) );
						});
						if (ct && ct.length) $('##_swcat').val(ct);
						$('.sw_ctselect').show();
					}
				} else {
					$('.sw_ctselect').hide();
					$('.sw_camselect').hide();
				}
				$('##_swcat').multiselect('refresh');
				onChangeSWCreditType();
			}
			function onChangeSWCreditType() {
				var aidlist = $('##_swca').val() || '';
				var ctlist = $('##_swcat').val() || '';
				$('##_swcam').find('option').remove();

				// Show credit amount dropdown only if exactly 1 authority and 1 credit type are selected
				if (aidlist != '' && aidlist.length == 1 && ctlist != '' && ctlist.length == 1) {
					var objParams = {
						catalogOrgCode: '#attributes.data.catalogOrgCode#',
						authorityID: aidlist[0],
						creditType: ctlist[0]
					};

					var successCallback = function(data) {
						if (data && data.length > 0) {
							// Store currently selected values or use filter values from server
							var selectedValues = $('##_swcam').val() || [];
							var filterCreditAmounts = '#local.strFilters.creditAmountList#'.split(',').filter(function(v) { return v.trim() !== ''; });

							$.each(data, function(i, item) {
								var isSelected = false;
								// Check if this option should be selected based on current selection or filter
								if (selectedValues.length > 0) {
									isSelected = selectedValues.indexOf(item.value.toString()) !== -1;
								} else if (filterCreditAmounts.length > 0) {
									isSelected = filterCreditAmounts.indexOf(item.value.toString()) !== -1;
								}

								var option = $('<option>', {
									value: item.value,
									text: item.display
								});

								if (isSelected) {
									option.prop('selected', true);
								}

								$('##_swcam').append(option);
							});
							$('.sw_camselect').show();
							$('##_swcam').multiselect('refresh');
						} else {
							$('.sw_camselect').hide();
						}
					};

					var errorCallback = function() {
						$('.sw_camselect').hide();
					};

					TS_AJX('SWBROWSE', 'getCreditAmountsForAuthorityAndType', objParams, successCallback, errorCallback, 10000, errorCallback);
				} else {
					$('.sw_camselect').hide();
					$('##_swcam').val('');
				}
			}
			function clearAllSWFilters() {
				$('input[name="_swft"]').prop('checked',true);
				$('input[name="_swir"],input[name="_swis"]').prop('checked',false);
				$('##_swkwl,##_sws,##_swca,##_swa,##_swp').val('');
				$('##_swca').trigger('change');
				$('.sw_filterSummary').remove();
				searchSWPrograms();
			}
			function clearSWKeywordFilter(el) {
				var keyword = $(el).find('span').text();
				var arrKeywords = $('##_swkwl').val().split('~');
				if (arrKeywords.length) {
					var keywordIndex = arrKeywords.indexOf(keyword);
					if (keywordIndex != -1) {
						arrKeywords.splice(keywordIndex,1);
						$('##_swkwl').val(arrKeywords.length ? arrKeywords.join('~') : '');
					
						$(el).parent().remove();
						if (!arrKeywords.length) 
							$('.sw_filterSummary').remove();
					}
				}
				searchSWPrograms();
			}
			function clearSWFormatsFilter(el) {
				$('input[name="_swft"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWMyProgramsFilter(el) {
				$('input[name="_swir"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWSavedProgramsFilter(el) {
				$('input[name="_swis"]:checked').trigger('click');
				$(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWCreditsFilter(el,mode) {
				$('##_swca').val('').trigger('change');
				$('##_swca').multiselect('refresh');
				$('##_swcam').val('');
				$('##_swcam').multiselect('refresh');
				$('.sw_camselect').hide();
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWSubjectsFilter(el,mode) {
				$('##_sws').val('');
				$('##_sws').multiselect('refresh');
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function clearSWAuthorsFilter(el,mode,aid) {
				$(el).parent().remove();
				if (mode == 'summary') {
					$('##_swa').val('');
					searchSWPrograms();
				} else {
					var arrAID = $('##_swa').val().split(',').map(Number);
					if (arrAID.indexOf(aid) != -1) {
						arrAID.splice(arrAID.indexOf(aid), 1);
						arrAID = arrAID.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
						$('##_swa').val(arrAID);
					}
				}
			}
			function clearSWPublishersFilter(el,mode) {
				$('##_swp').val('');
				$('##_swp').multiselect('refresh');
				if (mode == 'summary') $(el).parent().remove();
				searchSWPrograms();
			}
			function getFiltersUrlString() {
				var kw = $('##_swkw').val().trim();
				if (kw != '') {
					var arrKeywords = $('##_swkwl').val().length ? $('##_swkwl').val().split('~') : [];
					if ((arrKeywords.length && arrKeywords.indexOf(kw) == -1) || !arrKeywords.length) arrKeywords.push(kw);
					if (arrKeywords.length) $('##_swkwl').val(arrKeywords.join('~'));
				}
				return $("##frmFilterSWPrograms :input").not('##_swkw').filter(function(index, element) { return $(element).attr('name') !== undefined && $(element).val() != '';}).serialize();
			}
			function searchSWPrograms() {
				$('.swBrowsePrograms .filter-btn').addClass('disabled').html('<i class="icon-spinner icon-spin"></i> Applying Filters');
				location.href = swBrowseUrl + '&' + getFiltersUrlString();
			}
			function gotoSWBrowsePage(pos) {
				location.href = swBrowseUrl + (filtersURLQueryString.length > 0 ? '&' + filtersURLQueryString : '') + '&_sw_sp=' + pos;
			}
			function sortSWBrowse(opt, el) {
				if(el) $('.swBrowsePrograms .sortOptionsDropdown span.activeSortOption').text(el.html());
				$('##_sw_so').val(opt);
				searchSWPrograms();
			}
			function toggleProgramFilters(f){
				var toggleResult = function(r) {};
				$(".swBrowsePrograms .refine-btn").toggleClass("active swPrimaryBkgd swPrimary", f).toggleClass("swPrimary", !f);
				$(".swBrowsePrograms .swCatalogContentFrame").toggleClass("open", f).toggleClass("xs-open", !f);
				$(".swBrowsePrograms .swCatalogToolBarFrame .mutedHidden").toggleClass("hidden", f);
				$(".swBrowsePrograms .refine-btn span").text( f? 'Close Filters' : 'Refine Results' );
				
				var objParams = { isClosed:!f };
				TS_AJX('SWBROWSE','toggleSWProgramFilterMenu',objParams,toggleResult,toggleResult,10000,toggleResult);
			}

			$(function() {
				$(".swBrowsePrograms .formatAccordionGroup:not(.singleEntry) .checkbox input:checkbox, .swBrowsePrograms .myProgramsAccordionGroup .checkbox input:checkbox").change(function () {
					$(this).parents('.checkbox').toggleClass('muted');
				});

				$(".swBrowsePrograms .refine-btn").click(function(){
					var display = !$(".swBrowsePrograms .swCatalogContentFrame").hasClass("open");
					toggleProgramFilters(display);
				});

				$(".swBrowsePrograms .swCatalogTileBottomBox .swCatalogPointBtn a").click(function(){debugger;
					var $clickedBtn = $(this).parent();
					var $siblingBtns = $clickedBtn.siblings('.swCatalogPointBtn');

					// Close any open sibling buttons (mutual exclusivity)
					$siblingBtns.removeClass('open open1');
					$siblingBtns.find('span').text('More');

					// Toggle the clicked button
					$clickedBtn.removeClass('open').toggleClass("open1");

					$(this).children("span").text(function(i, v){
						return v === 'More' ? 'Less' : 'More'
					})
				});
				<cfif listLen(local.availableFormats) eq 1>
					$(".swBrowsePrograms input.formatCheckbox").on("click", function (e) { $(this).prop("checked",true); });
				</cfif>

				$(".swBrowsePrograms .swRateExpand > a").click(function(){
					$(this).parent().toggleClass("open");
					$(this).text(function(i, v){
						return v === 'View More' ? 'View Less' : 'View More';
					})
				});

				/* filter initially closed for mobile devices OR user closed filter */
				if ($(window).width() < 768 <cfif local.swCatalogFilterMenuIsClosed>|| true</cfif>) {
					toggleProgramFilters(false);
				}

				initSWBrowsePrograms();
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.browseProgramsJS#">

<cfoutput>
<div class="swCatalogWrapper swCatalog swBrowsePrograms">
	<cfif NOT structIsEmpty(local.strBrowse.strCarousel)>
		<div class="SWCatalogBanner">
			<div class="row-fluid">
				<div class="span12">
					#local.strBrowse.strCarousel.html#
				</div>
			</div>
		</div>
	</cfif>
	
	<div class="swCatalogToolBar first">
		<div class="flex swCatalogToolBarFrame">
			<div class="swCatalogToolBarFrameFormatIcon">
				<ul class="nav nav-tabs swFormatHead">
					<cfif listFindNoCase(local.availableFormats,"conf")>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=conf" class="mcEvent"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandConfTab#</a></li>
					</cfif>
					<cfif listFindNoCase(local.availableFormats,"swl")>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swl" class="swWebinar"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandSWLTab#</a></li>
					</cfif>
					<cfif listFindNoCase(local.availableFormats,"swod")>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swod" class="swOnDemand"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandSWODTab#</a></li>
					</cfif>
					<cfif listFindNoCase(local.availableFormats,"swb")>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=swb" class="swBundle"><i class="bi bi-basket" aria-hidden="true"></i>#attributes.data.semWeb.qrySWP.brandBundleTab#</a></li>
					</cfif>
				</ul>
			</div>
			<div class="ml-auto visible-tablet visible-desktop swCatalogToolBarFrameIconBar">
				<ul class="nav nav-tabs pull-right">
					<li>
						<a href="##" onclick="viewSWSavedPrograms();return false;" class="swPrimary tooltip-icon swSavedProgramsIconLink <cfif val(attributes.data.savedProgramsCount) eq 0>muted</cfif>" rel="tooltip" data-placement="bottom" title="<cfif attributes.data.savedProgramsCount>View saved</cfif>">
							<i class="bi bi-heart-fill sw_savedprogramscounticon <cfif attributes.data.savedProgramsCount>swRed swIconBadge<cfelse>muted</cfif>" data-swsavedprogramscount="#attributes.data.savedProgramsCount#" aria-hidden="true"></i>
						</a>
					</li>
					<li>
						<cfif attributes.data.swRegCart.recordCount>
							<cfif NOT local.isBot>
								<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="View cart">
									<i class="bi bi-cart-fill sw_regcartcounticon swIconBadge" data-swregcartcount="#attributes.data.swRegCart.recordcount#" aria-hidden="true"></i>
								</a>
							</cfif>
						<cfelse>
							<a href="javascript:void(0);" class="muted">
								<i class="bi bi-cart-fill" aria-hidden="true"></i>
							</a>
						</cfif>
					</li>
					<li><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="#attributes.data.qrySWP.brandMyCLETab#"><i class="bi bi-person-lines-fill" aria-hidden="true"></i></a></li>
					<li><a href="#attributes.event.getValue('mainurl')#&panel=showFAQ" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="FAQ"><i class="bi bi-question-circle-fill" aria-hidden="true"></i></a></li>
					<li><a href="#attributes.event.getValue('mainurl')#" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="Catalog Home"><i class="bi bi-house-fill" aria-hidden="true"></i></a></li>
				</ul>
			</div>
		</div>
	</div>

	<div class="swCatalogToolBar second flex">
		<div class="row-fluid swCatalogToolBarFrame">
			<div class="span4">
				<a href="javascript:void(0);" class="btn btn-primary tooltip-icon refine-btn active swPrimaryBkgd swPrimaryBkgdHover swWhiteHoverText" rel="tooltip" title="" data-original-title="Open filtering options">
					<i class="bi bi-sliders"></i><span>Close Filters</span></a>
				<p id="swActiveFiltersCount" class="muted hidden mutedHidden"></p>
			</div>
			<div class="span4 text-center">
				<cfif local.strBrowse.strPagination.totalCount gt 0>
					<cfif local.strBrowse.strPagination.totalCount GT local.strBrowse.strPagination.count>
						<p class="paginationInfo">showing #local.strBrowse.strPagination.startPos#-<cfif ((local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count) - 1) lte local.strBrowse.strPagination.totalCount>#local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count - 1#<cfelse>#local.strBrowse.strPagination.totalCount#</cfif> of #local.strBrowse.strPagination.totalCount# results</p>
					<cfelse>
						<p class="paginationInfo">showing #local.strBrowse.strPagination.totalCount# result<cfif local.strBrowse.strPagination.totalCount gt 1>s</cfif>
					</cfif>
				</cfif>
			</div>
			<div class="span4 text-right">
				<div class="last">
					<div class="visible-phone alpha-sort">
						<a class="dropdown-toggle swPrimary" href="##" data-toggle="dropdown"><i class="bi bi-sort-alpha-down drop-icon" aria-hidden="true"></i></a>
						<ul class="dropdown-menu">
							<li>Sort by:</li>
							<cfloop array="#local.sortOptionsArr#" item="local.thisOption">
								<li><a href="##" onclick="sortSWBrowse('#local.thisOption.key#'); return false;">#local.thisOption.value#</a></li>
							</cfloop>
						</ul>
					</div>
					<div class="visible-phone alpha-sort">
						<a class="dropdown-toggle swPrimary" href="##" data-toggle="dropdown"><i class="bi bi-three-dots-vertical drop-icon" aria-hidden="true"></i></a>
						<ul class="dropdown-menu">
							<li>
								<a href="##" onclick="viewSWSavedPrograms();return false;" class="savedProgramsMobileMenuItem <cfif not attributes.data.savedProgramsCount>muted</cfif>">
									<i class="bi bi-heart-fill" aria-hidden="true"></i>View Saved <span class="savedCount"><cfif attributes.data.savedProgramsCount>(#attributes.data.savedProgramsCount#)</cfif></span>
								</a>
							</li>
							<li>
								<cfif attributes.data.swRegCart.recordCount>
									<cfif NOT local.isBot>
										<a href="#attributes.event.getValue('mainurl')#&panel=showCart"><i class="bi bi-cart-fill" aria-hidden="true"></i>View Cart <span class="cartCount">(#attributes.data.swRegCart.recordCount#)</span></a>
									</cfif>
								<cfelse>
									<a href="javascript:void(0);" class="muted"><i class="bi bi-cart-fill" aria-hidden="true"></i>View cart</a>
								</cfif>
							</li>
							<li><a href="#attributes.event.getValue('mainurl')#&panel=My"><i class="bi bi-person-lines-fill" aria-hidden="true"></i>#attributes.data.qrySWP.brandMyCLETab#</a></li>
							<li><a href="#attributes.event.getValue('mainurl')#&panel=showFAQ"><i class="bi bi-question-circle-fill" aria-hidden="true"></i>FAQ</a></li>
							<li><a href="#attributes.event.getValue('mainurl')#"><i class="bi bi-house-fill" aria-hidden="true"></i>Catalog Home</a></li>
						</ul>
					</div>
					
					<div class="dropdown sortOptionsDropdown">
						<a class="btn dropdown-toggle sw-d-flex" data-toggle="dropdown" href="javascript:void(0);">
							<span class="mr-auto activeSortOption">#local.activeSortOptionText#</span> <i class="bi bi-chevron-compact-down"></i>
						</a>
						<div class="dropdown-menu text-left">
							<cfloop array="#local.sortOptionsArr#" item="local.thisOption">
								<li><a href="##" onclick="sortSWBrowse('#local.thisOption.key#', $(this)); return false;" class="swPrimaryBkgdHover swWhiteHoverText">#local.thisOption.value#</a></li>
							</cfloop>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<div class="swBrowseProgramContentArea">
		<div class="row-fluid">
			<div class="span12">
				<div class="swCatalogContentFrame open">
					<div class="span3">
						<div class="swBrowseProgramLeftContentArea">
							<div class="collapseSections swFiltering">
								<form name="frmFilterSWPrograms" id="frmFilterSWPrograms" onsubmit="searchSWPrograms(); return false;">
									<input type="hidden" name="_swkwl" id="_swkwl" value="#local.strFilters.keywordsList#">
									<input type="hidden" name="_swa" id="_swa" value="#local.strFilters.authorIDList#">
									<input type="hidden" name="_sw_so" id="_sw_so" value="#local.strFilters.sortoption#">

									<div class="swCatalogSideBarTop">
										<cfif local.strFilters.hasSearchParams>
											<ul class="sw_filterSummary">
												<cfloop list="#local.strFilters.keywordsList#" index="local.thisKeyword" delimiters="~">
													<li>
														<span class="badge" onclick="clearSWKeywordFilter(this);">
															<i class="bi bi-x" aria-hidden="true"></i>"<span>#local.thisKeyword#</span>"
														</span>
													</li>
												</cfloop>
												<cfif listLen(local.strFilters.formats)>
													<li>
														<span class="badge" onclick="clearSWFormatsFilter(this);">
															<i class="bi bi-x" aria-hidden="true"></i><span>format (#listLen(local.strFilters.formats)#)</span>
														</span>
													</li>
												</cfif>
												<cfif local.strFilters.isRegistered>
													<li>
														<span class="badge" onclick="clearSWMyProgramsFilter(this);">
															<i class="bi bi-x" aria-hidden="true"></i><span>Registered</span>
														</span>
													</li>
												</cfif>
												<cfif local.strFilters.isSaved>
													<li>
														<span class="badge" onclick="clearSWSavedProgramsFilter(this);">
															<i class="bi bi-x" aria-hidden="true"></i><span>saved</span>
														</span>
													</li>
												</cfif>
												<cfif listLen(local.strFilters.creditAuthorityList)>
													<li>
														<span class="badge" onclick="clearSWCreditsFilter(this,'summary');">
															<i class="bi bi-x" aria-hidden="true"></i><span>credit (#listLen(local.strFilters.creditAuthorityList)#)</span>
														</span>
													</li>
												</cfif>
												<cfif listLen(local.strFilters.subjects)>
													<li>
														<span class="badge" onclick="clearSWSubjectsFilter(this,'summary');">
															<i class="bi bi-x" aria-hidden="true"></i><span>subject areas (#listLen(local.strFilters.subjects)#)</span>
														</span>
													</li>
												</cfif>
												<cfif listLen(local.strFilters.authorIDList)>
													<li>
														<span class="badge" onclick="clearSWAuthorsFilter(this,'summary');">
															<i class="bi bi-x" aria-hidden="true"></i><span>speaker (#listLen(local.strFilters.authorIDList)#)</span>
														</span>
													</li>
												</cfif>
												<cfif listLen(local.strFilters.participantIDList)>
													<li>
														<span class="badge" onclick="clearSWPublishersFilter(this,'summary');">
															<i class="bi bi-x" aria-hidden="true"></i><span>publisher (#listLen(local.strFilters.participantIDList)#)</span>
														</span>
													</li>
												</cfif>
											</ul>
											<div class="text-right sw_filterSummary">
												<small><a href="##" class="clear muted" onclick="clearAllSWFilters();return false;">clear filters</a></small>
											</div>
										</cfif>
										<div class="swCatalogSearchKey keywordSearchBox">
											<input type="text" name="_swkw" id="_swkw" placeholder="keyword" autocomplete="off">
											<button type="submit"><i class="bi bi-search"></i></button>
										</div>
									</div>
									<div class="accordion parentAccordion">
										<div class="accordion-group formatAccordionGroup<cfif listLen(local.availableFormats) eq 1> singleEntry</cfif>">
											<div class="accordion-heading"> <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapseFormats" aria-expanded="true"> Format <i class="bi bi-chevron-compact-down"></i> </a> </div>
											<div id="collapseFormats" class="accordion-body collapse" style="height: 0px;">
												<div class="accordion-inner">
													<fieldset>
														<cfif listFindNoCase(local.availableFormats,"conf")>
															<label class="checkbox<cfif NOT listFindNoCase(local.strFilters.formats,'conf')> muted</cfif>">
																<small>
																	<input type="checkbox" name="_swft" value="conf" class="formatCheckbox"<cfif listFindNoCase(local.strFilters.formats,'conf')> checked</cfif>>
																	<span></span> #attributes.data.semWeb.qrySWP.brandConfTab#
																</small>
															</label>
														</cfif>
														<cfif listFindNoCase(local.availableFormats,"swl")>
															<label class="checkbox<cfif NOT listFindNoCase(local.strFilters.formats,'swl')> muted</cfif>">
																<small>
																	<input type="checkbox" name="_swft" value="swl" class="formatCheckbox"<cfif listFindNoCase(local.strFilters.formats,'swl')> checked</cfif>>
																	<span></span> #attributes.data.semWeb.qrySWP.brandSWLTab#
																</small>
															</label>
														</cfif>
														<cfif listFindNoCase(local.availableFormats,"swod")>
															<label class="checkbox<cfif NOT listFindNoCase(local.strFilters.formats,'swod')> muted</cfif>">
																<small>
																	<input type="checkbox" name="_swft" value="swod" class="formatCheckbox"<cfif listFindNoCase(local.strFilters.formats,'swod')> checked</cfif>>
																	<span></span> #attributes.data.semWeb.qrySWP.brandSWODTab#
																</small>
															</label>
														</cfif>
														<cfif listFindNoCase(local.availableFormats,"swb")>
															<label class="checkbox<cfif NOT listFindNoCase(local.strFilters.formats,'swb')> muted</cfif>">
																<small>
																	<input type="checkbox" name="_swft" value="swb" class="formatCheckbox"<cfif listFindNoCase(local.strFilters.formats,'swb')> checked</cfif>>
																	<span></span> #attributes.data.semWeb.qrySWP.brandBundleTab#
																</small>
															</label>
														</cfif>
													</fieldset>
												</div>
											</div>
										</div>
										<div class="accordion-group myProgramsAccordionGroup">
											<div class="accordion-heading">
												<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapseMyPrograms">
													My Programs <i class="bi bi-chevron-compact-down"></i>
												</a>
											</div>
											<div id="collapseMyPrograms" class="accordion-body collapse">
												<div class="accordion-inner">
													<fieldset>
														<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
															<label class="checkbox<cfif NOT local.strFilters.isRegistered> muted</cfif>">
																<small>
																	<input type="checkbox" name="_swir" value="1"<cfif local.strFilters.isRegistered> checked</cfif>>
																	<span></span> Registered
																</small>
															</label>
														</cfif>
														<label class="checkbox<cfif NOT local.strFilters.isSaved> muted</cfif>">
															<small>
																<input type="checkbox" name="_swis" value="1"<cfif local.strFilters.isSaved> checked</cfif>>
																<span></span> Saved
															</small>
														</label>
													</fieldset>
												</div>
											</div>
										</div>
										<cfif arrayLen(local.strBrowse.arrCreditsInfo)>
											<div class="accordion-group">
												<div class="accordion-heading"> <a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapseCreditsInfo"> Credit <i class="bi bi-chevron-compact-down"></i> </a> </div>
												<div id="collapseCreditsInfo" class="accordion-body collapse">
													<div class="accordion-inner">
														<fieldset>
															<p><small<cfif NOT listLen(local.strFilters.creditAuthorityList)> class="muted"</cfif>>Credit Authority:</small></p>
															<select name="_swca" id="_swca" multiple="multiple" onchange="onChangeSWCreditAuthority();">
																<cfloop array="#local.strBrowse.arrCreditsInfo#" index="local.thisCreditInfo">
																	<option value="#local.thisCreditInfo.authorityID#" data-swcredittypes="#lCase(structKeyList(local.thisCreditInfo.strCreditTypes))#"<cfif listFind(local.strFilters.creditAuthorityList,local.thisCreditInfo.authorityID)> selected</cfif>>#local.thisCreditInfo.authorityName#</option>
																</cfloop>
															</select>
															<div class="sw_ctselect sw-mt-3">
																<p><small<cfif NOT listLen(local.strFilters.creditTypes)> class="muted"</cfif>>Credit Types:</small></p>
																<select id="_swcat" name="_swcat" multiple="multiple" onchange="onChangeSWCreditType();"></select>
															</div>
															<div class="sw_camselect sw-mt-3" style="display:none;">
																<p><small <cfif NOT listLen(local.strFilters.creditAmountList)> class="muted"</cfif>>Credit Amount:</small></p>
																<select id="_swcam" name="_swcam" multiple="multiple"></select>
															</div>
														</fieldset>
														<div class="text-right">
															<small><a href="##" class="clear muted" onclick="clearSWCreditsFilter(this,'select');return false;">clear</a></small>
														</div>
													</div>
												</div>
											</div>
										</cfif>
										<cfif local.strBrowse.qryCategories.recordCount>
											<div class="accordion-group">
												<div class="accordion-heading">
													<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapseCategories"> Subject Areas <i class="bi bi-chevron-compact-down"></i> </a> 
												</div>
												<div id="collapseCategories" class="accordion-body collapse">
													<div class="accordion-inner">
														<select name="_sws" id="_sws" multiple="multiple">
															<cfloop query="local.strBrowse.qryCategories">
																<option value="#local.strBrowse.qryCategories.categoryValue#"<cfif listFindNoCase(local.strFilters.subjects,local.strBrowse.qryCategories.categoryValue)> selected</cfif>>#local.strBrowse.qryCategories.categoryName#</option>
															</cfloop>
														</select>
														<div class="text-right">
															<small><a href="##" class="clear muted" onclick="clearSWSubjectsFilter(this,'select');return false;">clear</a></small>
														</div>
													</div>
												</div>
											</div>
										</cfif>
										<cfif arrayLen(local.strBrowse.arrAuthors)>
											<div class="accordion-group">
												<div class="accordion-heading">
													<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapseAuthors"> Speaker <i class="bi bi-chevron-compact-down"></i> </a>
												</div>
												<div id="collapseAuthors" class="accordion-body collapse">
													<div class="accordion-inner">
														<p><small class="muted">Begin typing then click or tap the speaker's name to add to filters.</small></p>
														<div class="swCatalogSideBarTop">
															<ul id="sw_speakerslist">
																<cfif listLen(local.strFilters.authorIDList)>
																	<cfloop array="#local.strBrowse.arrAuthors#" index="local.thisAuthor">
																		<cfif listFind(local.strFilters.authorIDList,local.thisAuthor.authorid)>
																			<li>
																				<a href="##" class="swPrimary" onclick="clearSWAuthorsFilter(this,'list',#local.thisAuthor.authorid#);return false;">
																					<i class="bi bi-x" aria-hidden="true"></i> #local.thisAuthor.authorname#
																				</a>
																			</li>
																		</cfif>
																	</cfloop>
																</cfif>
															</ul>
															<div class="swCatalogSearchKey">
																<input type="text" name="sw_speakers" id="sw_speakers" class="sw-pr-0" placeholder="Start typing name...">
															</div>
														</div>
													</div>
												</div>
											</div>
										</cfif>
										<cfif local.strBrowse.qryPublishers.recordCount gt 1>
											<div class="accordion-group">
												<div class="accordion-heading">
													<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="##accordion2" href="##collapsePublishers"> Publisher <i class="bi bi-chevron-compact-down"></i> </a>
												</div>
												<div id="collapsePublishers" class="accordion-body collapse">
													<div class="accordion-inner">
														<select name="_swp" id="_swp" multiple="multiple">
															<cfloop query="local.strBrowse.qryPublishers">
																<option value="#local.strBrowse.qryPublishers.participantID#"<cfif listFindNoCase(local.strFilters.participantIDList,local.strBrowse.qryPublishers.participantID)> selected</cfif>>#local.strBrowse.qryPublishers.publisherName#</option>
															</cfloop>
														</select>
														<div class="text-right">
															<small><a href="##" class="clear muted" onclick="clearSWPublishersFilter(this,'select');return false;">clear</a></small>
														</div>
													</div>
												</div>
											</div>
										</cfif>
									</div>
									<a href="##" class="btn btn-primary swPrimaryBkgd filter-btn" onclick="searchSWPrograms();return false;">Apply Filters</a>
								</form>
							</div>
						</div>
					</div>
					<div class="span9">
						<div class="swResults">
							<div class="text-center visible-phone">
								<p class="muted"><em>showing #local.strBrowse.strPagination.startPos#-<cfif ((local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count) - 1) lte local.strBrowse.strPagination.totalCount>#(local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count) - 1#<cfelse>#local.strBrowse.strPagination.totalCount#</cfif> of #local.strBrowse.strPagination.totalCount# results</em></p>
							</div>
							<div class="swCatalogTiles">
								<cfif arrayLen(local.strBrowse.arrPrograms)>
									<cfloop array="#local.strBrowse.arrPrograms#" index="local.thisProgram">
										<cfswitch expression="#local.thisProgram.ft#">
											<cfcase value="SWL">
												<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.programID#">
												<cfif len(local.thisProgram.dspCredits)>
													<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{creditsViewLink}}', local.programDetailPageLink & "&jumpTo=creditSection")/>
													<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{classList}}', "swPrimary")/>
												</cfif>
												<cfif len(local.thisProgram.programSubTitle)>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
												<cfelse>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
												</cfif>
												<!--- is registration closed? reg closes when event starts or when allowregistrants is 0--->
												<cfset local.isRegOpen = true>
												<cfif now() gte local.thisProgram.dspStartDate OR NOT local.thisProgram.allowRegistrants>
													<cfset local.isRegOpen = false>
												</cfif>
												
												<div class="swResultsRow swCatalogTileResult">
													<div class="row-fluid">
														<div class="span12 swCatalogTitleTopBox">
															<cfif len(local.thisProgram.featuredImagePath)>
																<div class="span3 swCatalogTileBox">
																	<div class="swCatalogImgBox">
																		<img src="#local.thisProgram.featuredImagePath#" />
																	</div>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swl" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</div>
															</cfif>
															<div class="<cfif len(local.thisProgram.featuredImagePath)>span5 swCatalogMiddleColumn<cfelse>span7 swCatalogMiddleColumn swCatalogMiddleColumnWithoutPhotos</cfif>">
																<cfif NOT len(local.thisProgram.featuredImagePath)>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill muted sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swl" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill muted swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle#></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</cfif>
																<cfif local.thisProgram.isFeatured><span class="label featuredLabel swPrimaryBkgd">Featured</span></cfif>
																<a href="##" class="swWebinar visible-phone swProgramBrand"><i class="bi bi-laptop" aria-hidden="true"></i>#local.thisProgram.programBrand#</a>
																<a href="#local.programDetailPageLink#"><h2 class="prgmTitle swPrimary sw-mt-0">#local.thisProgram.programTitle#</h2></a>
																<cfif len(local.thisProgram.programSubTitle)>
																	<h2 class="prgmSubtitle sw-mt-0"><small class="swPrimary">#local.thisProgram.programSubTitle#</small></h2>
																</cfif>
																<p class="prgmDate">#Replace(DateTimeFormat(local.thisProgram.dspStartDate,'dddd, m/d/yyyy - h:nn aa'),":00 ","")# #UCASE(local.thisProgram.dspTZ)#</p>
																<cfif len(local.thisProgram.dspCredits)><p class="prgmCredits">#local.thisProgram.dspCredits#</p></cfif>
															</div>
															<div class="span4 swCatalogRightColumn visible-desktop visible-tablet">
																<cfset local.showCreditRibbon = attributes.data.semWeb.qrySWP.showCreditRibbon and local.thisProgram.uptoCreditsCount gt 0>
																<div class="swCatalogProgramContent <cfif local.showCreditRibbon>span9<cfelse>span12</cfif>">
																	<p class="swWebinar swFormatLabel"><i class="bi bi-laptop" aria-hidden="true"></i>#local.thisProgram.programBrand#</p>
																	<cfif len(local.thisProgram.incBundlesList)>
																		<p><small>Available in bundle(s).</small></p>
																	</cfif>
																	<cfif local.thisProgram.linkedSWODSeminarID gt 0><p><small>Recording will be available on-demand.</small></p></cfif>
																	<cfif local.thisProgram.attended>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Attended<small><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary sw-ml-2">#attributes.data.qrySWP.brandMyCLETab#</a></small></p>
																	<cfelseif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																		<div class="swRateExpand">
																	</cfif>
																	<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																		<p class="sw-mb-0">
																			<cfif local.thisRate.price gt 0>
																				#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																			<cfelse>
																				#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
																			</cfif>
																		</p>
																	</cfloop>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																			<p></p>
																			<a href="javascript:void(0);" class="swPrimary">View More</a>
																		</div>
																	</cfif>
																</div>
																<cfif local.showCreditRibbon>
																	<div class="span3 text-right swCatalogCreditBox">
																		<a href="javascript:void(0);" class="text-center swCatalogCreditBoxCard swPrimaryBkgd">
																			<h6>up to</h6>
																			<h3>#local.thisProgram.uptoCreditsCount#</h3>
																			<p>Credit<cfif local.thisProgram.uptoCreditsCount gt 1>s</cfif></p>
																		</a>
																	</div>
																</cfif>
															</div>
														</div>
													</div>
													<div class="row-fluid clearfix sw-mt-3">
														<div class="span12 swCatalogTileBottomBox">
															<ul>
																<li class="swCatalogPointBtn">
																	<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																		<a href="javascript:void(0);" class="swPrimary swLearningObj">
																			#attributes.data.semWeb.qrySWP.brandLearnObjectives# <span class="visible-phone">More</span>
																		</a>
																		<ul class="swCatalogPoints">
																			<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																				<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																			</cfloop>
																		</ul>
																	<cfelse>
																		<a href="javascript:void(0);" class="swPrimary visible-phone">
																			<span>More</span>
																		</a>
																	</cfif>
																</li>
																<!--- Speakers Section 
																<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
																	<li class="swCatalogPointBtn">
																		<a href="javascript:void(0);" class="swPrimary swSpeakersObj">
																			Speakers <span class="visible-phone">More</span>
																		</a>
																		<ul class="swCatalogPoints">
																			<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
																				<li class="swSpeakersPoint">#local.thisSpeaker#</li>
																			</cfloop>
																		</ul>
																	</li>
																</cfif>--->
																	<div class="swCatalogGrayBox visible-phone">
																		<cfif len(local.thisProgram.dspCredits)>
																			<div>
																				<h5 class="swPrimary">Credits</h5>
																				<p class="prgmCredits">#local.thisProgram.dspCredits#</p>
																			</div>
																		</cfif>
																		<div>
																			<cfif local.thisProgram.linkedSWODSeminarID gt 0><small>Recording will be available on-demand.</small></cfif>
																			<h5 class="swPrimary">pricing</h5>
																			<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																				<p class="sw-mb-0">
																					<cfif local.thisRate.price gt 0>
																						#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																					<cfelse>
																						#local.thisProgram.freeRateDisplay# for #local.thisRate.description#
																					</cfif>
																				</p>
																			</cfloop>
																		</div>
																		<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																			<div>
																				<h5 class="swPrimary">#uCase(attributes.data.semWeb.qrySWP.brandLearnObjectives)#</h5>
																				<ul>
																					<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																						<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																					</cfloop>
																				</ul>
																			</div>
																		</cfif>
																	</div>
																	<cfif local.thisProgram.attended>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Attended</p>
																	<cfelseif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																</li>
																<li>
																	<ul class="btnBox">
																		<li><a href="#local.programDetailPageLink#" class="swPrimary viewDetails">View Details</a></li>
																		<cfif NOT local.isBot>
																			<li>
																				<cfif local.thisProgram.inCart>
																					<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">In Cart</a>
																				<cfelseif local.isRegOpen AND len(local.thisProgram.arrRates)>
																					<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWL-#local.thisProgram.programID#" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">Add to cart</a>
																				</cfif>
																			</li>
																		</cfif>
																	</ul>
																</li>
															</ul>
														</div>
													</div>
												</div>
											</cfcase>
											<cfcase value="SWOD">
												<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.programID#">
												<cfif len(local.thisProgram.dspCredits)>
													<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{creditsViewLink}}', local.programDetailPageLink & "&jumpTo=creditSection")/>
													<cfset local.thisProgram.dspCredits = replace(local.thisProgram.dspCredits,'{{classList}}', "swPrimary")/>
												</cfif>
												<cfif len(local.thisProgram.programSubTitle)>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
												<cfelse>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
												</cfif>
												<!--- is registration closed? reg closes when event starts or when allowregistrants is 0--->
												<cfset local.isRegOpen = true>
												<cfif NOT local.thisProgram.allowRegistrants>
													<cfset local.isRegOpen = false>
												</cfif>

												<div class="swResultsRow swCatalogTileResult">
													<div class="row-fluid">
														<div class="span12 swCatalogTitleTopBox">
															<cfif len(local.thisProgram.featuredImagePath)>
																<div class="span3 swCatalogTileBox">
																	<div class="swCatalogImgBox">
																		<img src="#local.thisProgram.featuredImagePath#" />
																		<cfif NOT local.isBot and len(local.thisProgram.videoPreviewLink)>
																			<div class="overlay">
																				<a href="##swVideoPreviewModal" data-toggle="modal" rel="tooltip" data-placement="right" class="swvideopreview tooltip-icon" title="Preview" data-swvideopreviewlink="#jsStringFormat(local.thisProgram.videoPreviewLink)#" data-swvidepreviewdisplayname="#jsStringFormat(local.thisProgram.videoPreviewDisplayName)#">
																					<i class="bi bi-play-circle" aria-hidden="true"></i>
																				</a>
																			</div>
																		</cfif>
																	</div>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swod" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</div>
															</cfif>
															<div class="<cfif len(local.thisProgram.featuredImagePath)>span5 swCatalogMiddleColumn<cfelse>span7 swCatalogMiddleColumn swCatalogMiddleColumnWithoutPhotos</cfif>">
																<cfif NOT len(local.thisProgram.featuredImagePath)>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill muted sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swod" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill muted swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle#></span></li>
																				</ul>
																			</li>
																			<cfif NOT local.isBot and len(local.thisProgram.videoPreviewLink)>
																				<li>
																					<a href="##swVideoPreviewModal" data-toggle="modal" rel="tooltip" data-placement="right" class="tooltip-icon swvideopreview" title="Preview" data-swvideopreviewlink="#jsStringFormat(local.thisProgram.videoPreviewLink)#" data-swvidepreviewdisplayname="#jsStringFormat(local.thisProgram.videoPreviewDisplayName)#">
																						<i class="bi bi-play-circle muted swPrimaryHover" aria-hidden="true"></i>
																					</a>
																				</li>
																			</cfif>
																		</ul>
																	</div>
																</cfif>
																<cfif local.thisProgram.isFeatured><span class="label featuredLabel swPrimaryBkgd">Featured</span></cfif>
																<a href="##" class="swOnDemand visible-phone swProgramBrand"><i class="bi bi-play-circle" aria-hidden="true"></i>#local.thisProgram.programBrand#</a>
																<a href="#local.programDetailPageLink#"><h2 class="prgmTitle swPrimary sw-mt-0">#local.thisProgram.programTitle#</h2></a>
																<cfif len(local.thisProgram.programSubTitle)>
																	<h2 class="prgmSubtitle sw-mt-0"><small class="swPrimary">#local.thisProgram.programSubTitle#</small></h2>
																</cfif>
																<cfif len(local.thisProgram.dspCredits)><p class="prgmCredits">#local.thisProgram.dspCredits#</p></cfif>
															</div>
															<div class="span4 swCatalogRightColumn visible-desktop visible-tablet">
																<cfset local.showCreditRibbon = attributes.data.semWeb.qrySWP.showCreditRibbon and local.thisProgram.uptoCreditsCount gt 0>
																<div class="swCatalogProgramContent <cfif local.showCreditRibbon>span9<cfelse>span12</cfif>">
																	<p class="swOnDemand swFormatLabel"><i class="bi bi-play-circle" aria-hidden="true"></i>#local.thisProgram.programBrand#</p>
																	<cfif len(local.thisProgram.incBundlesList)>
																		<p><small>Available in bundle(s).</small></p>
																	</cfif>
																	<cfif local.thisProgram.passed>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Completed<small><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary sw-ml-2">#attributes.data.qrySWP.brandMyCLETab#</a></small></p>
																	<cfelseif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																		<div class="swRateExpand">
																	</cfif>
																	<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																		<p class="sw-mb-0">
																			<cfif local.thisRate.price gt 0>
																				#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																			<cfelse>
																				#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
																			</cfif>
																		</p>
																	</cfloop>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																			<p></p>
																			<a href="javascript:void(0);" class="swPrimary">View More</a>
																		</div>
																	</cfif>
																</div>
																<cfif local.showCreditRibbon>
																	<div class="span3 text-right swCatalogCreditBox">
																		<a href="javascript:void(0);" class="text-center swCatalogCreditBoxCard swPrimaryBkgd">
																			<h6>up to</h6>
																			<h3>#local.thisProgram.uptoCreditsCount#</h3>
																			<p>Credit<cfif local.thisProgram.uptoCreditsCount gt 1>s</cfif></p>
																		</a>
																	</div>
																</cfif>
															</div>
														</div>
													</div>
													<div class="row-fluid clearfix sw-mt-3">
														<div class="span12 swCatalogTileBottomBox">
															<ul>
																<li class="swCatalogPointBtn">
																	<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																		<a href="javascript:void(0);" class="swPrimary swLearningObj">
																			#attributes.data.semWeb.qrySWP.brandLearnObjectives# <span class="visible-phone">More</span>
																		</a>
																		<ul class="swCatalogPoints">
																			<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																				<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																			</cfloop>
																		</ul>
																	<cfelse>
																		<a href="javascript:void(0);" class="swPrimary visible-phone">
																			<span>More</span>
																		</a>
																	</cfif>
																	<!--- Speakers Section --->
																	<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
																		<a href="javascript:void(0);" class="swPrimary swLearningObj">
																			Speakers <span class="visible-phone">More</span>
																		</a>
																		<ul class="swCatalogPoints">
																			<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
																				<li class="swLearningObjPoint">#local.thisSpeaker#</li>
																			</cfloop>
																		</ul>
																	</cfif>
																	<!-- Gray Box (Visible on Phone) -->
																	<div class="swCatalogGrayBox visible-phone">
																		<cfif len(local.thisProgram.dspCredits)>
																			<div>
																				<h5 class="swPrimary">Credits</h5>
																				<p class="prgmCredits">#local.thisProgram.dspCredits#</p>
																			</div>
																		</cfif>
																		<div>
																			<h5 class="swPrimary">pricing</h5>
																			<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																				<p class="sw-mb-0">
																					<cfif local.thisRate.price gt 0>
																						#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																					<cfelse>
																						#local.thisProgram.freeRateDisplay# for #local.thisRate.description#
																					</cfif>
																				</p>
																			</cfloop>
																		</div>
																		<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																			<div>
																				<h5 class="swPrimary">#uCase(attributes.data.semWeb.qrySWP.brandLearnObjectives)#</h5>
																				<ul>
																					<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																						<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																					</cfloop>
																				</ul>
																			</div>
																		</cfif>
																	</div>
																	<cfif local.thisProgram.passed>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Completed<small><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary sw-ml-2">#attributes.data.qrySWP.brandMyCLETab#</a></small></p>
																	<cfelseif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																</li>
																<li>
																	<ul class="btnBox">
																		<li><a href="#local.programDetailPageLink#" class="swPrimary viewDetails">View Details</a></li>
																		<cfif NOT local.isBot>
																			<li>
																				<cfif local.thisProgram.inCart>
																					<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">In Cart</a>
																				<cfelseif local.isRegOpen AND len(local.thisProgram.arrRates)>
																					<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWOD-#local.thisProgram.programID#" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">Add to cart</a>
																				</cfif>
																			</li>
																		</cfif>
																	</ul>
																</li>
															</ul>
														</div>
													</div>
												</div>
											</cfcase>
											<cfcase value="EV">
												<cfif len(local.thisProgram.programSubTitle)>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.thisProgram.eventDetailLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
												<cfelse>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.thisProgram.eventDetailLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
												</cfif>

												<div class="swResultsRow swCatalogTileResult">
													<div class="row-fluid">
														<div class="span12 swCatalogTitleTopBox">
															<cfif len(local.thisProgram.featuredImagePath)>
																<div class="span3 swCatalogTileBox">
																	<div class="swCatalogImgBox">
																		<img src="#local.thisProgram.featuredImagePath#" />
																	</div>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="ev" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</div>
															</cfif>
															<div class="<cfif len(local.thisProgram.featuredImagePath)>span5 swCatalogMiddleColumn<cfelse>span7 swCatalogMiddleColumn swCatalogMiddleColumnWithoutPhotos</cfif>">
																<cfif NOT len(local.thisProgram.featuredImagePath)>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill muted sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="ev" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li><a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill muted swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle#></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</cfif>
																<cfif local.thisProgram.isFeatured><span class="label featuredLabel swPrimaryBkgd">Featured</span></cfif>
																<a href="##" class="mcEvent visible-phone"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</a>
																<a href="#local.thisProgram.eventDetailLink#"><h2 class="prgmTitle swPrimary sw-mt-0">#local.thisProgram.programTitle#</h2></a>
																<cfif len(local.thisProgram.programSubTitle)>
																	<h2 class="prgmSubtitle sw-mt-0"><small class="swPrimary">#local.thisProgram.programSubTitle#</small></h2>
																</cfif>
																<p class="prgmDate">#Replace(DateTimeFormat(local.thisProgram.displayStartTime,'dddd, m/d/yyyy - h:nn aa'),":00 ","")# #UCASE(local.thisProgram.displayTimeZoneAbbr)#</p>
																<cfif len(local.thisProgram.programLocation)>
																	<p class="prgmLocation">#local.thisProgram.programLocation#</p>
																</cfif>
															</div>
															<div class="span4 swCatalogRightColumn visible-desktop visible-tablet">
																<div class="swCatalogProgramContent span9">
																	<p class="mcEvent swFormatLabel"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</p>
																	<cfif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																		<div class="swRateExpand">
																	</cfif>
																	<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																		<p class="sw-mb-0">
																			<cfif local.thisRate.rate gt 0>
																				#Replace(DollarFormat(local.thisRate.rate),'.00','')##local.thisProgram.displayedCurrencyType# #local.thisRate.rateName#
																			<cfelse>
																				#Replace(local.thisRate.freeRateDisplay,'.00','')# for #local.thisRate.rateName#
																			</cfif>
																		</p>
																	</cfloop>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																			<p></p>
																			<a href="javascript:void(0);" class="swPrimary">View More</a>
																		</div>
																	</cfif>
																</div>
															</div>
														</div>
													</div>
													<div class="row-fluid clearfix sw-mt-3">
														<cfif NOT len(local.thisProgram.arrRates) and local.thisProgram.isRegistered>
															<div class="text-right visible-phone">
																<p class="swPrimary attended swAttended sw-mb-0"><i class="bi bi-person-check-fill"></i> Registered</p>
															</div>
														</cfif>
														<div class="span12 swCatalogTileBottomBox">
															<ul>
																<cfif len(local.thisProgram.arrRates)>
																	<li class="swCatalogPointBtn">
																		<a href="javascript:void(0);" class="swPrimary visible-phone">
																			<span>More</span>
																		</a>
																		<div class="swCatalogGrayBox visible-phone">
																			<div>
																				<h5 class="swPrimary">pricing</h5>
																				<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																					<p class="sw-mb-0">
																						<cfif local.thisRate.rate gt 0>
																							#Replace(DollarFormat(local.thisRate.rate),'.00','')##local.thisProgram.displayedCurrencyType# #local.thisRate.rateName#
																						<cfelse>
																							#Replace(local.thisRate.freeRateDisplay,'.00','')# for #local.thisRate.rateName#
																						</cfif>
																					</p>
																				</cfloop>
																			</div>
																		</div>
																		<cfif local.thisProgram.isRegistered>
																			<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																		</cfif>
																	</li>
																</cfif>
																<li>
																	<ul class="btnBox">
																		<li><a href="#local.thisProgram.eventDetailLink#" target="_blank" class="swPrimary viewDetails">View Details</a></li>
																		<cfif NOT local.isBot>
																			<li>
																				<cfif local.thisProgram.inCart>
																					<a href="/?pg=events&regcartv2" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">In Cart</a>
																				<cfelseif local.thisProgram.allowRegister and (local.thisProgram.showRates or len(local.thisProgram.altRegistrationURL))>
																					
																					<a  href="#local.thisProgram.eventRegV2Link#" target="_blank" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">Add to cart</a>
																				</cfif>
																			</li>
																		</cfif>
																	</ul>
																</li>
															</ul>
														</div>
													</div>
												</div>
											</cfcase>
											<cfcase value="SWB">
												<cfset local.programDetailPageLink = "#attributes.event.getValue('mainurl')#&panel=showBundle&bundleid=#local.thisProgram.programID#">
												<cfif len(local.thisProgram.programSubTitle)>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#: #jsStringFormat(local.thisProgram.programSubTitle)#"'>
												<cfelse>
													<cfset local.shareThisURLAndTitle = 'st_url="#attributes.data.semWeb.baseurl##local.programDetailPageLink#" st_title="#jsStringFormat(local.thisProgram.programTitle)#"'>
												</cfif>
												
												<div class="swResultsRow swCatalogTileResult">
													<div class="row-fluid">
														<div class="span12 swCatalogTitleTopBox">
															<cfif len(local.thisProgram.featuredImagePath)>
																<div class="span3 swCatalogTileBox">
																	<div class="swCatalogImgBox">
																		<img src="#local.thisProgram.featuredImagePath#" />
																	</div>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swb" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li class=""> <a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#" st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle# st_image="#attributes.data.semWeb.baseurl##local.thisProgram.featuredImagePath#"></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</div>
															</cfif>
															<div class="<cfif len(local.thisProgram.featuredImagePath)>span5 swCatalogMiddleColumn<cfelse>span7 swCatalogMiddleColumn swCatalogMiddleColumnWithoutPhotos</cfif>">
																<cfif NOT len(local.thisProgram.featuredImagePath)>
																	<div class="swCatalogIconBox">
																		<ul>
																			<cfif NOT local.isBot>
																				<li>
																					<a href="javascript:void(0);" rel="tooltip" data-placement="right" title="" class="swHeartIcon tooltip-icon" title="Save for later"><i class="bi bi-heart-fill muted sw_saveforlater<cfif local.thisProgram.isSaved> swRed</cfif>" data-swprogramid="#local.thisProgram.programID#" data-swprogramtype="swb" data-swsaveforlatermode="browse" aria-hidden="true"></i></a>
																				</li>
																			</cfif>
																			<li><a href="##" class="dropdown-toggle" data-toggle="dropdown"><i class="bi bi-share-fill muted swAttendedHover" aria-hidden="true"></i></a>
																				<ul class="dropdown-menu">
																					<li><span class="st_email" #local.shareThisURLAndTitle# st_summary="Check out this program offered by #attributes.data.semWeb.qrySWP.description#"></span></li>
																					<li><span class="st_facebook" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_linkedin" #local.shareThisURLAndTitle#></span></li>
																					<li><span class="st_twitter" #local.shareThisURLAndTitle#></span></li>
																				</ul>
																			</li>
																		</ul>
																	</div>
																</cfif>
																<cfif local.thisProgram.isFeatured><span class="label featuredLabel swPrimaryBkgd">Featured</span></cfif>
																<a href="##" class="swBundle visible-phone swProgramBrand"><i class="bi bi-basket-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</a>
																<a href="#local.programDetailPageLink#"><h2 class="prgmTitle swPrimary sw-mt-0">#local.thisProgram.programTitle#</h2></a>
																<cfif len(local.thisProgram.programSubTitle)>
																	<h2 class="prgmSubtitle sw-mt-0"><small class="swPrimary">#local.thisProgram.programSubTitle#</small></h2>
																</cfif>
																<cfif NOT local.thisProgram.isSWOD>
																	<cfset local.displayDate = "#DateTimeFormat(local.thisProgram.dspStartDate,'dddd, m/d/yyyy')#" & (DateDiff("d",local.thisProgram.dspStartDate,local.thisProgram.dspEndDate) gt 0 ? " - #DateTimeFormat(local.thisProgram.dspEndDate,'dddd, m/d/yyyy')#" : "")>
																	<p class="prgmDate">#local.displayDate#</p>
																</cfif>
															</div>
															<div class="span4 swCatalogRightColumn visible-desktop visible-tablet">
																<div class="swCatalogProgramContent span9">
																	<p class="swBundle swFormatLabel"><i class="bi bi-basket-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</p>
																	<cfif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																		<div class="swRateExpand">
																	</cfif>
																	<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																		<p class="sw-mb-0">
																			<cfif local.thisRate.price gt 0>
																				#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																			<cfelse>
																				#Replace(local.thisProgram.freeRateDisplay,'.00','')# for #local.thisRate.description#
																			</cfif>
																		</p>
																	</cfloop>
																	<cfif arrayLen(local.thisProgram.arrRates) gt 3>
																			<p></p>
																			<a href="javascript:void(0);" class="swPrimary">View More</a>
																		</div>
																	</cfif>
																</div>
															</div>
														</div>
													</div>
													<div class="row-fluid clearfix sw-mt-3">
														<div class="span12 swCatalogTileBottomBox">
															<ul>
																<li class="swCatalogPointBtn">
																	<a href="javascript:void(0);" class="swPrimary swLearningObj">
																		<cfif arrayLen(local.thisProgram.arrIncPrograms)>Included Programs</cfif>
																		<span class="visible-phone">More</span>
																	</a>
																	<ul class="swCatalogPoints">
																		<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																			<li class="swLearningObjLabel">#attributes.data.semWeb.qrySWP.brandLearnObjectives#</li>
																		</cfif>
																		<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																			<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																		</cfloop>
																		<!--- Speakers Section 
																		<cfif structKeyExists(local.thisProgram, "arrSpeakers") AND arrayLen(local.thisProgram.arrSpeakers)>
																			<li class="swCatalogPointBtn">
																				<a href="javascript:void(0);" class="swPrimary swSpeakersObj">
																					Speakers <span class="visible-phone">More</span>
																				</a>
																				<ul class="swCatalogPoints">
																					<cfloop array="#local.thisProgram.arrSpeakers#" index="local.thisSpeaker">
																						<li class="swSpeakersPoint">#local.thisSpeaker#</li>
																					</cfloop>
																				</ul>
																			</li>
																		</cfif>--->
																		
																		<cfif arrayLen(local.thisProgram.arrLearningObjectives) and arrayLen(local.thisProgram.arrIncPrograms)>
																			<li class="swIncProgramLabel">Included Programs</li>
																		</cfif>
																		<cfloop array="#local.thisProgram.arrIncPrograms#" index="local.thisIncludedProgram">
																			<li class="swIncProgramPoint">#local.thisIncludedProgram.contentName#<cfif NOT local.thisProgram.isSWOD> (#DateFormat(local.thisIncludedProgram.dateStart,'m/d/yyyy')#)</cfif></li>
																		</cfloop>
																	</ul>
																	<div class="swCatalogGrayBox visible-phone">
																		<div>
																			<h5 class="swPrimary">pricing</h5>
																			<cfloop array="#local.thisProgram.arrRates#" index="local.thisRate">
																				<p class="sw-mb-0">
																					<cfif local.thisRate.price gt 0>
																						#Replace(DollarFormat(local.thisRate.price),'.00','')#<cfif attributes.data.semWeb.qrySWP.showUSD> USD</cfif> for #local.thisRate.description#
																					<cfelse>
																						#local.thisProgram.freeRateDisplay# for #local.thisRate.description#
																					</cfif>
																				</p>
																			</cfloop>
																		</div>
																		<cfif arrayLen(local.thisProgram.arrLearningObjectives)>
																			<div>
																				<h5 class="swPrimary">#uCase(attributes.data.semWeb.qrySWP.brandLearnObjectives)#</h5>
																				<ul>
																					<cfloop array="#local.thisProgram.arrLearningObjectives#" index="local.thisObjective">
																						<li class="swLearningObjPoint">#local.thisObjective.objective#</li>
																					</cfloop>
																				</ul>
																			</div>
																		</cfif>
																		<cfif arrayLen(local.thisProgram.arrIncPrograms)>
																			<div>
																				<h5 class="swPrimary">INCLUDED PROGRAMS</h5>
																				<ul>
																					<cfloop array="#local.thisProgram.arrIncPrograms#" index="local.thisIncludedProgram">
																						<li class="swIncProgramPoint">#local.thisIncludedProgram.contentName#</li>
																					</cfloop>
																				</ul>
																			</div>
																		</cfif>
																	</div>
																	<cfif local.thisProgram.isRegistered>
																		<p class="swPrimary attended swAttended"><i class="bi bi-person-check-fill"></i> Registered</p>
																	</cfif>
																</li>
																<li>
																	<ul class="btnBox">
																		<li><a href="#local.programDetailPageLink#" class="swPrimary viewDetails">View Details</a></li>
																		<cfif NOT local.isBot>
																			<li>
																				<cfif local.thisProgram.inCart>
																					<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">In Cart</a>
																				<cfelseif len(local.thisProgram.arrRates)>
																					<a href="#attributes.event.getValue('mainurl')#&panel=reg&item=SWB-#local.thisProgram.programID#" class="btn swPrimaryBorder swPrimary swPrimaryBkgdHover swWhiteHoverText addcart">Add to cart</a>
																				</cfif>
																			</li>
																		</cfif>
																	</ul>
																</li>
															</ul>
														</div>
													</div>
												</div>
											</cfcase>
										</cfswitch>
									</cfloop>
								<cfelse>
									<div class="alert sw-mt-4">
										Sorry, we didn't find any programs to display.
									</div>
								</cfif>

								<cfif local.strBrowse.strPagination.numTotalPages gt 1 and local.strBrowse.strPagination.numCurrentPage lt local.strBrowse.strPagination.numTotalPages>
									<a href="##" onclick="gotoSWBrowsePage(#local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count#);return false;" class="swPrimary visible-phone swCatalogLoadMore">+ LOAD MORE</a>
								</cfif>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<cfif local.strBrowse.strPagination.numTotalPages gt 1>
		<div class="pagination">
			<ul>
				<cfif local.strBrowse.strPagination.numCurrentPage gt 1>
					<li>
						<a href="##" onclick="gotoSWBrowsePage(1);return false;" title="First Page" class="swPrimary">&lt;&lt;</a>
					</li>
					<li>
						<a href="##" onclick="gotoSWBrowsePage(#local.strBrowse.strPagination.startPos - local.strBrowse.strPagination.count#);return false;" title="Previous Page" class="swPrimary">&lt;</a>
					</li>
				</cfif>

				<cfloop from="#local.strBrowse.strPagination.startPage#" to="#local.strBrowse.strPagination.endPage#" index="local.i">
					<cfset local.thisStartRow = local.i>
					<cfif local.i gt 1>
						<cfset local.thisStartRow = (local.strBrowse.strPagination.count * (local.i-1)) + 1>
					</cfif>
					
					<cfif local.strBrowse.strPagination.numCurrentPage eq local.i>
						<li class="active"><a href="javascript:void(0);" class="swPrimary">#local.i#</a></li>
					<cfelse>
						<li><a href="##" onclick="gotoSWBrowsePage(#local.thisStartRow#);return false;" class="swPrimary">#local.i#</a></li>
					</cfif>
				</cfloop>
				
				<cfif local.strBrowse.strPagination.numCurrentPage lt local.strBrowse.strPagination.numTotalPages>
					<li>
						<a href="##" onclick="gotoSWBrowsePage(#local.strBrowse.strPagination.startPos + local.strBrowse.strPagination.count#);return false;" title="Next Page" class="swPrimary">
							&gt;
						</a>
					</li>
					<li>
						<a href="##" onclick="gotoSWBrowsePage(#(local.strBrowse.strPagination.count * (local.strBrowse.strPagination.numTotalPages-1)) + 1#);return false;" title="Last Page" class="swPrimary">
							&gt;&gt;
						</a>
					</li>
				</cfif>
			</ul>
		</div>
	</cfif>

	<cfif NOT local.isBot>
		<!--- Video Preview Modal --->
		<div id="swVideoPreviewModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="swVideoPreviewLabel" aria-hidden="true">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<div class="badge badge-info">Preview</div>
				<h4 id="swVideoPreviewLabel"></h4>
			</div>
			<div class="modal-body sw-pt-0">
				<video id="previewPlayer" class="video-js sw-w-100">
					<p class="vjs-no-js">To view this video please enable JavaScript, and consider upgrading to a web browser that <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a></p>
				</video>
			</div>
		</div>
	</cfif>
</div>
</cfoutput>