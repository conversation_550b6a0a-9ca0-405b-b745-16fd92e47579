<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript">
		function saveSponsorGroup() {
			mca_hideAlert('err_frmeditsponsorgroup');

			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.loadSponsors_#local.widgetSelectorID#();
					top.MCModalUtils.hideModal();
				} else {
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to update this sponsor grouping. Please try again.';
					mca_showAlert('err_frmeditsponsorgroup', errMsg);
					top.$('##btnMCModalSave').prop('disabled', false).html('Save Changes');
				}
			};

			if($('##sponsorGroupName').val().trim() == ''){
				mca_showAlert('err_frmeditsponsorgroup', 'Please enter a name for the sponsor grouping.');
				return false;
			}

			top.$('##btnMCModalSave').prop('disabled', true).html('Saving...');
			var objParams = {
				sponsorGroupingID: #val(local.qryGrouping.sponsorGroupingID)#,
				sponsorGrouping: $('##sponsorGroupName').val().trim(),
				referenceType: '#local.referenceType#',
				referenceID: #local.referenceID#
			};

			TS_AJX('SPONSORS','updateSponsorGrouping',objParams,saveResult,saveResult,10000,saveResult);
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<div id="err_frmeditsponsorgroup" class="alert alert-danger mb-3 d-none"></div>
<form name="frmEditSponsorGroup" id="frmEditSponsorGroup" onsubmit="saveSponsorGroup(); return false;">
	<div class="form-label-group mb-3">
		<input type="text" name="sponsorGroupName" id="sponsorGroupName" class="form-control" value="#encodeForHTMLAttribute(local.qryGrouping.sponsorGrouping)#" maxlength="100">
		<label for="sponsorGroupName">Name of Sponsor Grouping</label>
	</div>
	<!--- Hidden submit button triggered from parent modal --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>