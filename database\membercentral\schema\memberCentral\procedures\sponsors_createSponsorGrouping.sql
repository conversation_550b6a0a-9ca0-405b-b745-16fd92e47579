CREATE PROC dbo.sponsors_createSponsorGrouping
@siteID int,
@referenceType varchar(20),
@referenceID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sponsorGroupingOrder int;

	SET @sponsorGroupingID = NULL;

	IF EXISTS (
		SELECT sponsorGroupingID
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceID = @referenceID
		AND referenceType = @referenceType
		AND sponsorGrouping = @sponsorGrouping
	)
		RAISERROR('Sponsor Grouping Exists.',16,1);

	SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1
	FROM dbo.sponsorsGrouping
	WHERE siteID = @siteID
	AND referenceID = @referenceID
	AND referenceType = @referenceType;
			
	INSERT INTO dbo.sponsorsGrouping(sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, referenceID)
	VALUES (@sponsorGrouping, @sponsorGroupingOrder, @siteID, @referenceType, @referenceID);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO