ALTER PROC dbo.sponsors_getSponsorsByReferenceID
@siteID int,
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	WITH SponsorsGroupings AS (
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = @referenceType
		AND referenceID = @referenceID
			UNION ALL
		SELECT 0, 'Default - No Grouping', 0
	)
	SELECT sg.sponsorGroupingID, sg.sponsorGrouping, sg.sponsorGroupingOrder, su.sponsorUsageID, s.sponsorID, s.sponsorName, su.sponsorOrder
	FROM dbo.sponsorsUsage AS su
	INNER JOIN dbo.sponsors as s on s.siteID = @siteID
		AND s.sponsorID = su.sponsorID
	LEFT OUTER JOIN dbo.sponsorsUsageGrouping AS sug ON sug.sponsorUsageID = su.sponsorUsageID
	LEFT OUTER JOIN SponsorsGroupings AS sg ON sg.sponsorGroupingID = ISNULL(sug.sponsorGroupingID,0)
	WHERE su.referenceType = @referenceType
	AND su.referenceID = @referenceID
	ORDER BY sg.sponsorGroupingOrder, su.sponsorOrder;
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO