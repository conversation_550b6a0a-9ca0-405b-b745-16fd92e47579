<cfset local.selectedTab = arguments.event.getTrimValue("tab","afSettings")>
<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">

<cfsavecontent variable="local.formulaJS">
	<cfoutput>
	<script language="javascript">
		const mc_af_hasSaveRights = <cfif local.hasSaveRights>true<cfelse>false</cfif>;
		function saveAF() {
			if (!mc_af_hasSaveRights) return false;
			
			<cfif NOT val(local.qryAF.isSystemOnly) OR application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				var afName = $.trim($('##afName').val());
				if (afName.length == 0) {
					mca_showAlert('err_formula', 'An advancement formula label cannot be blank.');
					return false;
				}

				var numDate = parseInt($('##dateNum').val());
				if (isNaN(numDate)) numDate = 0;
				if (numDate <= 0) {
					mca_showAlert('err_formula', 'The advancement number must be more than 0.');
					return false;
				}

				top.$('##btnMCModalSave').attr('disabled',true);
				top.$('##btnMCModalSave').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Saving...');
				mca_hideAlert('err_formula');

				var saveAFResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						top.reloadAFs();
						top.MCModalUtils.hideModal();
					} else {
						mca_showAlert('err_formula', 'Unable to save advancement formula.<br/>' + r.errmsg);
						top.$('##btnMCModalSave').attr('disabled',false);
						top.$('##btnMCModalSave').html('Save Advancement Formula');
					}
				};
			
				var objParams = { afid:$('##afid').val(), afName:afName, dateNum:numDate, datePart:$('##datePart').val(), 
					adjustTerm:$('##selAdjustTerm').val(), nextWeekday:$('##nextWeekday').val(), 
					weekNumber:$('##selWeekNum').val(), afUID:$('##afUID').val() };
				TS_AJX('ADMADVFORM','saveAdvanceFormula',objParams,saveAFResult,saveAFResult,20000,saveAFResult);
			</cfif>
		}
		function updateAFExample() {
			$('##divExampleDate').html(mca_getLoadingHTML('Calculating...'));
			
			let satVal = $('##selAdjustTerm').val();
			let dpVal = $('##datePart').val();
			let nwVal = $('##nextWeekday').val();
			let wnVal = $('##selWeekNum').val();
			let numDate = parseInt($('##dateNum').val());
			if (isNaN(numDate)) numDate = 0;

			if (dpVal != 'M' && !$('##divAdjustTerm').hasClass('d-none')) {
				$('##divAdjustTerm').addClass('d-none');
				$('##selAdjustTerm').val('');
				satVal = '';
			} else if (dpVal == 'M' && $('##divAdjustTerm').hasClass('d-none')) {
				$('##divAdjustTerm').removeClass('d-none');
			}

			if ($('##selAdjustTerm').val() == 'SameDayWk') {
				if ($('##divWeekNumber').hasClass('d-none')) $('##divWeekNumber').removeClass('d-none');
				if (!$('##divNextWeekday').hasClass('d-none')) {
					$('##divNextWeekday').addClass('d-none');
					$('##nextWeekday').val(0);
					nwVal = 0;
				}
			} else {
				if (!$('##divWeekNumber').hasClass('d-none')) {
					$('##divWeekNumber').addClass('d-none');
					$('##selWeekNum').val('');
					wnVal = '';
				}
				if ($('##divNextWeekday').hasClass('d-none')) $('##divNextWeekday').removeClass('d-none');
			}

			if (numDate <= 0) {
				if (!$('##divExampleArea').hasClass('d-none')) $('##divExampleArea').addClass('d-none');
			} else {
				if ($('##divExampleArea').hasClass('d-none')) $('##divExampleArea').removeClass('d-none');
				var #toScript(local.AFCurrentDate, "txtNowDate")#
				var chkDateExResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true')
						$('##spanExampleDate').html(r.retdate);
					else 
						$('##spanExampleDate').html('<i>Unable to calculate</i>');
				};
				
				var objParams = { baseDate:txtNowDate, datePart:dpVal, dateNum:numDate, adjustTerm:satVal, nextWeekday:nwVal, weekNumber:wnVal };
				TS_AJX('ADMADVFORM','getAdvanceFormulaDate',objParams,chkDateExResult,chkDateExResult,20000,chkDateExResult);
			}
		}
		function onManageTabChangeHandler(ActiveTab) {
			switch (ActiveTab.id) {
				case 'afSettingsTab':
					buildAFSettingsTabFooter();
					break;
			
				default:
					top.MCModalUtils.buildFooter({});
			}
		}
		function buildAFSettingsTabFooter() {
			if (!mc_af_hasSaveRights) return false;

			top.$('##MCModalFooter').addClass('d-flex').removeClass('d-none');
			if (!top.$('##MCModalFooter ##btnMCModalSave').length) {
				top.MCModalUtils.buildFooter({
					classlist: 'd-flex',
					showclose: true,
					buttons: [
						{
							class: "btn-primary ml-auto py-1",
							clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmAF ##btnAFSub").click',
							label: 'Save Advancement Formula', 
							name: 'btnMCModalSave',
							id: 'btnMCModalSave'
						}
					]
				});
			}
			top.$('##MCModalFooter ##btnMCModalSave').prop('disabled',false);
		}

		$(function() {
			updateAFExample();
			<cfif local.afID EQ 0>
				buildAFSettingsTabFooter();
			<cfelse>
				mca_initNavPills('AFSettingTabs', '#local.selectedTab#', '#local.lockTab#', onManageTabChangeHandler);
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.formulaJS#">

<cfoutput>
<div class="p-2">
<cfif local.afID EQ 0>
	<cfinclude template="frm_advanceFormula_settings.cfm">
<cfelse>
	<ul id="AFSettingTabs" class="nav nav-line nav-line-alt">
		<cfset local.thisTabName = "afSettings">
		<cfset local.thisTabID = "afSettingsTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				Settings
				<div class="divider"></div>
			</a>
		</li>
		<cfset local.thisTabName = "afUsages">
		<cfset local.thisTabID = "afUsagesTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				Usage Details
				<div class="divider"></div>
			</a>
		</li>
	</ul>
	<div class="tab-content pt-3">
		<div class="tab-pane fade" id="tab-afSettingsTab" role="tabpanel" aria-labelledby="afSettings">
			<cfinclude template="frm_advanceFormula_settings.cfm">
		</div>
		<div class="tab-pane fade" id="tab-afUsagesTab" role="tabpanel" aria-labelledby="afUsages">			
			<div>The following areas of your site use this advance formula.</div>
			<table class="mt-4 table table-sm table-striped">
				<thead>
					<tr>
						<th>##</th>
						<th>Usage Area</th>
					</tr>
				</thead>
				<tbody>
					<cfif local.qryAFUsage.recordCount>
						<cfloop query="local.qryAFUsage">
							<tr>
								<td>#local.qryAFUsage.usageCount#</td>
								<td>#local.qryAFUsage.usageName#</td>
							</tr>
						</cfloop>
					<cfelse>
						<tr><td colspan="2" class="text-center">No usages found.</td></tr>
					</cfif>
				</tbody>
			</table>
		</div>
	</div>
</cfif>
</div>
</cfoutput>