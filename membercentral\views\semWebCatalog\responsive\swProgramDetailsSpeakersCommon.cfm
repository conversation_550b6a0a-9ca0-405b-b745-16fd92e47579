<cfoutput>
<!--- Group sponsors by sponsorGrouping --->
<cfset local.sponsorGroups = {}>
<cfset local.groupOrder = {}>
<cfloop query="local.qrySponsors">
	<cfset local.groupKey = len(trim(local.qrySponsors.sponsorGrouping)) ? local.qrySponsors.sponsorGrouping : "Default - No Grouping">
	<cfset local.groupOrderValue = val(local.qrySponsors.sponsorGroupingOrder) gt 0 ? local.qrySponsors.sponsorGroupingOrder : 999>

	<cfif NOT structKeyExists(local.sponsorGroups, local.groupKey)>
		<cfset local.sponsorGroups[local.groupKey] = []>
		<cfset local.groupOrder[local.groupKey] = local.groupOrderValue>
	</cfif>

	<cfset local.imageSource = "">
	<cfif val(local.qrySponsors.featureImageID) gt 0 AND fileExists("#local.featuredThumbImageFullRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#")>
		<cfset local.imageSource = "#local.featuredThumbImageRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#">
	</cfif>

	<cfset arrayAppend(local.sponsorGroups[local.groupKey], {
		sponsorName = local.qrySponsors.sponsorName,
		sponsorContent = local.qrySponsors.sponsorContent,
		sponsorUrl = local.qrySponsors.sponsorUrl,
		imageSource = local.imageSource,
		sponsorOrder = local.qrySponsors.sponsorOrder
	})>
</cfloop>

<!--- Sort groups by order --->
<cfset local.sortedGroups = []>
<cfloop collection="#local.sponsorGroups#" item="local.groupName">
	<cfset arrayAppend(local.sortedGroups, {
		name = local.groupName,
		order = local.groupOrder[local.groupName],
		sponsors = local.sponsorGroups[local.groupName]
	})>
</cfloop>
<cfset arraySort(local.sortedGroups, function(a, b) { return compare(a.order, b.order); })>

<!--- Display grouped sponsors --->
<cfloop array="#local.sortedGroups#" index="local.group">
	<!--- Display group name if not default --->
	<cfif local.group.name NEQ "Default - No Grouping">
		<div class="sponsor-group-header" data-sponsor-group="#encodeForHTMLAttribute(local.group.name)#">
			<h5 class="sponsor-group-name">#local.group.name#</h5>
		</div>
	</cfif>

	<!--- Display sponsors in this group --->
	<cfset arraySort(local.group.sponsors, function(a, b) { return compare(a.sponsorOrder, b.sponsorOrder); })>
	<cfloop array="#local.group.sponsors#" index="local.sponsor">
		<div class="sponsorContainer sw-mb-3 sponsor-item" data-sponsor-group="#encodeForHTMLAttribute(local.group.name)#">
			<div class="sw-mb-2">
				<cfif len(local.sponsor.sponsorUrl)>
					<a href="#local.sponsor.sponsorUrl#" class="swPrimary" target="_blank"><b>#local.sponsor.sponsorName#</b></a>
				<cfelse>
					<b>#local.sponsor.sponsorName#</b>
				</cfif>
			</div>
			<cfif len(local.sponsor.imageSource) or len(local.sponsor.sponsorContent)>
				<div class="row-fluid">
					<cfif len(local.sponsor.imageSource)>
						<div class="span2 sponsorImage">
							<cfif len(local.sponsor.sponsorUrl)>
								<a href="#local.sponsor.sponsorUrl#" target="_blank"><img src="#local.sponsor.imageSource#" alt=""></a>
							<cfelse>
								<img src="#local.sponsor.imageSource#" alt="">
							</cfif>
						</div>
					</cfif>
					<cfif len(local.sponsor.sponsorContent)>
						<div class="<cfif len(local.sponsor.imageSource)>span10<cfelse>span12</cfif> sponsorDesc">
							#local.sponsor.sponsorContent#
						</div>
					</cfif>
				</div>
			</cfif>
		</div>
	</cfloop>
</cfloop>
</cfoutput>